<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net8.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="FluentValidation" Version="12.0.0" />
      <PackageReference Include="MediatR" Version="12.5.0" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Enums\" />
      <Folder Include="Exceptions\" />
      <Folder Include="Mapping\" />
      <Folder Include="Pagination\" />
      <Folder Include="Repositories\" />
      <Folder Include="Services\" />
    </ItemGroup>

</Project>
