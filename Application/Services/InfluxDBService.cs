using InfluxDB.Client;

namespace VirtuManagerBackend.Application.Services;

public class InfluxDbService
{
    private readonly string _token;

    private readonly string _host;

    private readonly string _organization;

    private readonly string _bucket;

    private readonly string _username;

    private readonly string _password;

    public InfluxDbService(IConfiguration configuration)
    {
        var token = configuration.GetValue<string>("InfluxDB:Token");
        var host = configuration.GetValue<string>("InfluxDB:Host");
        var org = configuration.GetValue<string>("InfluxDB:Organization");
        var bucket = configuration.GetValue<string>("InfluxDB:Bucket");
        var username = configuration.GetValue<string>("InfluxDB:Username");
        var password = configuration.GetValue<string>("InfluxDB:Password");

        ArgumentNullException.ThrowIfNull(username);
        ArgumentNullException.ThrowIfNull(password);
        ArgumentNullException.ThrowIfNull(org);
        ArgumentNullException.ThrowIfNull(bucket);
        ArgumentNullException.ThrowIfNull(token);
        ArgumentNullException.ThrowIfNull(host);

        _username = username;
        _password = password;
        _organization = org;
        _bucket = bucket;
        _token = token;
        _host = host;
    }

    public void Write(Action<WriteApi> action)
    {
        using var client = new InfluxDBClient(new InfluxDBClientOptions(_host)
        {
            Username = _username,
            Password = _password,
            Bucket = _bucket,
            Org = _organization,
            Token = _token,
        });
        using var write = client.GetWriteApi();
        action(write);
    }

    public async Task<T> QueryAsync<T>(Func<QueryApi, Task<T>> action)
    {
        using var client = new InfluxDBClient(new InfluxDBClientOptions(_host)
        {
            Username = _username,
            Password = _password,
            Bucket = _bucket,
            Org = _organization,
            Token = _token,
        });
        var query = client.GetQueryApi();
        return await action(query);
    }
    
    public async Task<List<Dictionary<string, object>>> QueryVirtualMachineMetrics(string measurement, string field, string vmName, string range = "-1h")
    {
        return await QueryAsync<List<Dictionary<string, object>>>(async query =>
        {
            var flux = $@"
            from(bucket: ""{_bucket}"")
                |> range(start: {range})
                |> filter(fn: (r) => r[""_measurement""] == ""{measurement}"")
                |> filter(fn: (r) => r[""_field""] == ""{field}"")
                |> filter(fn: (r) => r[""vm""] == ""{vmName}"")
                |> yield(name: ""result"")
            ";

            var tables = await query.QueryAsync(flux, _organization);
            var results = new List<Dictionary<string, object>>();

            foreach (var table in tables)
            {
                foreach (var record in table.Records)
                {
                    results.Add(new Dictionary<string, object>
                    {
                        { "time", record.GetTime()?.ToUnixTimeMilliseconds() ?? 0 },
                        { "value", record.GetValue() }
                    });
                }
            }

            return results;
        });
    }
}