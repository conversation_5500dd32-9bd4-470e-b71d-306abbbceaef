namespace VirtuManagerBackend.Application.Api.Dto.VirtualMachine;

public record UpdateVirtualMachineDto
{
    public int VirtualMachineId { get; init; }
    
    public string Title { get; init; }
    
    public string UserName { get; init; }
    
    public string Password { get; init; }
    
    public ulong Ram { get; init; }
    
    public int CpuCores { get; init; }
    
    public string Sudo { get; init; }
    
    public string Shell { get; init; }

    public bool LockPassword { get; init; }
    
    public string HostName { get; init; }
}