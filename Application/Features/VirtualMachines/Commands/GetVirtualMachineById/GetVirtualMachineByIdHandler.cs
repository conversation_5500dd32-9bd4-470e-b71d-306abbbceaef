using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.VirtualMachine;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTable;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Application.Features.VirtualMachines.Commands.GetVirtualMachineById;

public class GetVirtualMachineByIdHandler : IRequestHandler<GetVirtualMachineByIdCommand, Result<VirtualMachine>>
{
    private readonly VirtualMachinesRepository _virtualMachinesRepository;
    
    public GetVirtualMachineByIdHandler(VirtualMachinesRepository virtualMachinesRepository)
    {
        _virtualMachinesRepository = virtualMachinesRepository;
    }
    
    public async Task<Result<VirtualMachine>> Handle(GetVirtualMachineByIdCommand request, CancellationToken cancellationToken)
    {
        var vm = await _virtualMachinesRepository.GetVirtualMachineById(request.Id);
        if (vm == null)
        {
            return Result.Fail(new Error("Virtual machine not found")
                .WithMetadata("Response", new VirtualMachineNotFoundResponse())
                .WithMetadata("StatusCode", StatusCodes.Status404NotFound));
        }
        
        return Result.Ok(vm);
    }
}