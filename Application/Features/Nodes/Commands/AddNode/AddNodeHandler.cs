using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.Node;
using VirtuManagerBackend.Domain.Db.Tables.NodeTable;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Application.Features.Nodes.Commands.AddNode;

public class AddNodeHandler : IRequestHandler<AddNodeCommand, Result<AddNodeResponse>>
{
    private readonly NodesRepository _nodesRepository;
    
    public AddNodeHandler(NodesRepository nodesRepository)
    {
        _nodesRepository = nodesRepository;
    }
    
    public async Task<Result<AddNodeResponse>> Handle(AddNodeCommand request, CancellationToken cancellationToken)
    {
        var nodeExists = await _nodesRepository.GetNodeByTitle(request.AddNodeDto.Title);

        if (nodeExists != null)
        {
            return Result.Fail(new Error("Node with this title already exists")
                .WithMetadata("Response", new AddNodeExistsResponse())
                .WithMetadata("StatusCode", StatusCodes.Status409Conflict));
        }

        var node = new Node
        {
            PrivateKey = Guid.NewGuid().ToString(),
            Title = request.AddNodeDto.Title,
        };

        await _nodesRepository.Add(node);
        return Result.Ok(new AddNodeResponse());
    }
}