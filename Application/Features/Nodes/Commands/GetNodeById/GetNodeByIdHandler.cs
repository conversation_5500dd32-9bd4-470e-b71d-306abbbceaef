using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.Node;
using VirtuManagerBackend.Features.Nodes.Commands.GetNodeById;
using VirtuManagerBackend.Infrastructure.Repositories;
using Error = FluentResults.Error;
using Node = VirtuManagerBackend.Domain.Db.Tables.NodeTable.Node;

namespace VirtuManagerBackend.Application.Features.Nodes.Commands.GetNodeById;

public class GetNodeByIdHandler : IRequestHandler<GetNodeByIdCommand, Result<Node>>
{
    private readonly NodesRepository _nodesRepository;
    
    public GetNodeByIdHandler(NodesRepository nodesRepository)
    {
        _nodesRepository = nodesRepository;
    }
    
    public async Task<Result<Node>> Handle(GetNodeByIdCommand request, CancellationToken cancellationToken)
    {
        var node = await _nodesRepository.GetNodeById(request.Id);
        if (node == null)
        {
            return Result.Fail(new Error("Node not found")
                .WithMetadata("Response", new NodeNotFoundResponse())
                .WithMetadata("StatusCode", StatusCodes.Status404NotFound));
        }

        return Result.Ok(node);
    }
}