using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.Node;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Application.Features.Nodes.Commands.UpdateNode;

public class UpdateNodeHandler : IRequestHandler<UpdateNodeCommand, Result<UpdateNodeResponse>>
{
    private readonly NodesRepository _nodesRepository;
    
    public UpdateNodeHandler(NodesRepository nodesRepository)
    {
        _nodesRepository = nodesRepository;
    }
    
    public async Task<Result<UpdateNodeResponse>> Handle(UpdateNodeCommand request, CancellationToken cancellationToken)
    {
        var nodeExists = await _nodesRepository.GetNodeById(request.Node.Id);

        if (nodeExists == null)
        {
            return Result.Fail(new Error("Node not found")
                .WithMetadata("Response", new NodeNotFoundResponse())
                .WithMetadata("StatusCode", StatusCodes.Status404NotFound));
        }

        nodeExists.Title = request.Node.Title;

        await _nodesRepository.Update(nodeExists);
        return Result.Ok(new UpdateNodeResponse());
    }
}