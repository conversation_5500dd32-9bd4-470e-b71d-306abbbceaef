using System.Security.Claims;
using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.User;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Application.Features.Users.Commands.DeleteUserById;

public class DeleteUserByIdHandler : IRequestHandler<DeleteUserByIdCommand, Result<UserDeletedResponse>>
{
    private readonly UsersRepository _usersRepository;
    
    public DeleteUserByIdHandler(UsersRepository usersRepository)
    {
        _usersRepository = usersRepository;
    }
    
    public async Task<Result<UserDeletedResponse>> Handle(DeleteUserByIdCommand request, CancellationToken cancellationToken)
    {
        var user = await _usersRepository.GetUserById(request.Id);
        if (user == null)
        {
            return Result.Fail(new Error("User not found")
                .WithMetadata("Response", new UserNotFoundResponse())
                .WithMetadata("StatusCode", StatusCodes.Status404NotFound));
        }

        var userLogin = request.User.Claims.FirstOrDefault(claim => claim.Type == ClaimTypes.Name);
        if (userLogin!.Value == user.Login)
        {
            return Result.Fail(new Error("User not delete yourself")
                .WithMetadata("Response", new UserNotDeleteYourselfResponse())
                .WithMetadata("StatusCode", StatusCodes.Status409Conflict));
        }
		
        await _usersRepository.Remove(user);
        return Result.Ok(new UserDeletedResponse());
    }
}