using System.Text.Json;
using FluentResults;
using MediatR;
using VirtuManagerBackend.Application.Api.Response.VirtualDisks;
using VirtuManagerBackend.Domain.Db.Tables;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTask;
using VirtuManagerBackend.Infrastructure.Repositories;
using VirtuManagerKafkaClient.Messaging.Interfaces;

namespace VirtuManagerBackend.Features.VirtualDisks.Commands.DeleteVirtualDisk;

public class DeleteVirtualDiskHandler : IRequestHandler<DeleteVirtualDiskCommand, Result<VirtualDiskDeletedResponse>>
{
    private readonly VirtualMachineTaskRepository _virtualMachineTaskRepository;
    
    private readonly IKafkaProducer<VirtualMachineTask> _kafkaProducer;
    
    public DeleteVirtualDiskHandler(VirtualMachineTaskRepository virtualMachineTaskRepository, IKafkaProducer<VirtualMachineTask> kafkaProducer)
    {
        _virtualMachineTaskRepository = virtualMachineTaskRepository;
        _kafkaProducer = kafkaProducer;
    }
    
    public async Task<Result<VirtualDiskDeletedResponse>> Handle(DeleteVirtualDiskCommand request, CancellationToken cancellationToken)
    {
        var task = new VirtualMachineTask
        {
            Payload = JsonSerializer.Serialize(request),
            Type = TaskType.RemoveDisk,
            CreatedAt = DateTime.Now,
            Status = TaskState.Pending,
            EndedAt = null
        };

        await _kafkaProducer.ProduceAsync(task, "virtual-machine-tasks", cancellationToken);
        
        await _virtualMachineTaskRepository.Add(task);
        return Result.Ok(new VirtualDiskDeletedResponse());
    }
}