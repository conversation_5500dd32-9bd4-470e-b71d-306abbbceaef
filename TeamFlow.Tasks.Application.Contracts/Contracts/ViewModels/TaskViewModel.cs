using TeamFlow.Tasks.Core.Shared.Enums;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;

public record TaskViewModel
{
    public string Title { get; init; }

    public string? Description { get; init; }

    public Guid ProjectId { get; init; }

    public Guid? AssignedId { get; init; }

    public Guid ReporterId { get; init; }

    public DateTime? DueDate { get; init; }

    public DateTime? CompletedAt { get; init; }

    public TaskStatuses Status { get; init; }

    public PriorityLevel Priority { get; init; }

    public TaskType Type { get; init; }

    public TimeSpan? EstimatedTime { get; init; }

    public TimeSpan? ActualTime { get; init; }

    public decimal? StoryPoints { get; init; }

    public List<string>? Tags { get; init; }

    public Guid? KanbanColumnId { get; init; }

    public int? KanbanOrder { get; init; }

    public DateTime CreatedAt { get; init; }

    public DateTime UpdatedAt { get; init; }
}