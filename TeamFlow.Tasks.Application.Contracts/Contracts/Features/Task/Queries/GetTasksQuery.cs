using FluentResults;
using MediatR;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Task;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Queries;

public record GetTasksQuery(Pageable Pageable, TaskFilterCriteria? Filter, TaskSortingCriteria? Sorting) : IRequest<Result<Page<TaskViewModel>>>;