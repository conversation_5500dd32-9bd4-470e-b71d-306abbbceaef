using FluentResults;
using MediatR;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Comment;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Queries;

public record GetCommentsQuery(Pageable Pageable, CommentFilterCriteria? Filter, CommentSortingCriteria? Sorting)
    : IRequest<Result<Page<CommentViewModel>>>;