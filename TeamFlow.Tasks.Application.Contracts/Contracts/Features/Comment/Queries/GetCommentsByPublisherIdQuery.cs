using FluentResults;
using MediatR;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Queries;

public record GetCommentsByPublisherIdQuery(Guid PublisherId) : IRequest<Result<IReadOnlyCollection<CommentViewModel>>>;

public record GetCommentsByPublisherIdPageableQuery(Pageable Pageable, Guid PublisherId)
    : IRequest<Result<Page<CommentViewModel>>>;