using FluentResults;
using MediatR;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Queries;

public record GetCommentsByParentCommentIdQuery(Guid ParentCommentId) : IRequest<Result<IReadOnlyCollection<CommentViewModel>>>;

public record GetCommentsByParentCommentIdPageableQuery(Pageable Pageable, Guid ParentCommentId)
    : IRequest<Result<Page<CommentViewModel>>>;