using FluentResults;
using MediatR;
using TeamFlow.Tasks.Core.Shared.Enums;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Features.Projects.Commands;

public record CreateProjectCommand(
    string Name, 
    string Description, 
    Guid OwnerId, 
    DateTime StartDate, 
    DateTime EndDate, 
    PriorityLevel PriorityLevel = PriorityLevel.Low,
    ProjectStatues Status = ProjectStatues.Planned)
    : IRequest<Result<Guid>>;