using FluentResults;
using MediatR;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Project;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Features.Projects.Queries;

public record GetProjectsQuery(Pageable Pageable, ProjectFilterCriteria? Filter, ProjectSortingCriteria? Sorting)
    : IRequest<Result<Page<ProjectViewModel>>>;