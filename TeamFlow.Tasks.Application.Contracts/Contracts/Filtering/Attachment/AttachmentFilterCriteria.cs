using System.Linq.Expressions;
using TeamFlow.Shared.Contracts.Filtering;
using TeamFlow.Shared.Utils;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Attachment;

public record AttachmentFilterCriteria(
    Guid? TaskId,
    Guid? UploaderId,
    string? FileName,
    string? OriginalFileName,
    string? ContentType,
    ulong? FileSize,
    string? SearchTerm) : IFilterCriteria<Core.Entities.Attachment>
{
    public string? SearchTerm { get; } = SearchTerm;

    public Expression<Func<Core.Entities.Attachment, bool>> ToPredicate()
    {
        var parameter = Expression.Parameter(typeof(Core.Entities.Attachment), "a");

        List<Expression?> predicates =
        [
            ExpressionBuilder.BuildEqual<Core.Entities.Attachment, Guid?>(parameter,
                nameof(Core.Entities.Attachment.TaskId), TaskId),
            ExpressionBuilder.BuildEqual<Core.Entities.Attachment, Guid?>(parameter,
                nameof(Core.Entities.Attachment.UploaderId), UploaderId),
            ExpressionBuilder.BuildStringContains<Core.Entities.Attachment>(parameter,
                nameof(Core.Entities.Attachment.FileName), FileName, true),
            ExpressionBuilder.BuildStringContains<Core.Entities.Attachment>(parameter,
                nameof(Core.Entities.Attachment.OriginalFileName), OriginalFileName, true),
            ExpressionBuilder.BuildStringContains<Core.Entities.Attachment>(parameter,
                nameof(Core.Entities.Attachment.ContentType), ContentType, true),
            ExpressionBuilder.BuildEqual<Core.Entities.Attachment, ulong?>(parameter,
                nameof(Core.Entities.Attachment.FileSize), FileSize)
        ];

        if (!string.IsNullOrWhiteSpace(SearchTerm))
        {
            var originalFileNameSearch = ExpressionBuilder.BuildStringContains<Core.Entities.Attachment>(parameter,
                nameof(Core.Entities.Attachment.OriginalFileName), OriginalFileName, true);

            var contentTypeSearch = ExpressionBuilder.BuildStringContains<Core.Entities.Attachment>(parameter,
                nameof(Core.Entities.Attachment.ContentType), ContentType, true);
            
            var searchPredicate = ExpressionBuilder.CombineWithOr(originalFileNameSearch, contentTypeSearch);
            predicates.Add(searchPredicate);
        }
        
        var body = ExpressionBuilder.CombineWithAnd(predicates.ToArray());
        
        return Expression.Lambda<Func<Core.Entities.Attachment, bool>>(body, parameter);
    }
}