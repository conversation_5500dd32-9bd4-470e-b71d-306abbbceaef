using System.Linq.Expressions;
using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Shared.Contracts.Sorting;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Comment;

public record CommentSortingCriteria(CommentsSortField SortField, SortDirection SortDirection) : ISortingCriteria<Core.Entities.Comment>
{
    public OrderByExpression<Core.Entities.Comment> ToOrderByExpression()
    {
        Expression<Func<Core.Entities.Comment, object>> keySelector = SortField switch
        {
            // AttachmentsSortField.FileName => x => x.FileName,
            CommentsSortField.CreatedAt => x => x.CreatedAt,
            CommentsSortField.UpdatedAt => x => x.UpdatedAt,
            CommentsSortField.MentionsCount => x => x.MentionedUserIds!.Count,
            CommentsSortField.ContentLength => x => x.Content.Length,
            CommentsSortField.ContentAlphabetical => x => x.Content,
            _ => x => x.Id,
        };
        
        return new OrderByExpression<Core.Entities.Comment>(keySelector, SortDirection);
    }
}