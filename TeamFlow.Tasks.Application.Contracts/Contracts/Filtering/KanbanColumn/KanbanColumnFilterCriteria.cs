using System.Linq.Expressions;
using TeamFlow.Shared.Contracts.Filtering;
using TeamFlow.Shared.Utils;

namespace TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.KanbanColumn;

public record KanbanColumnFilterCriteria(
    string? Name,
    Guid? ProjectId,
    int? Order,
    int? MinOrder,
    int? MaxOrder,
    string? SearchTerm) : IFilterCriteria<Core.Entities.KanbanColumn>
{
    public string? SearchTerm { get; } = SearchTerm;

    public Expression<Func<Core.Entities.KanbanColumn, bool>> ToPredicate()
    {
        var parameter = Expression.Parameter(typeof(Core.Entities.KanbanColumn), "kc");

        List<Expression?> predicates =
        [
            ExpressionBuilder.BuildStringContains<Core.Entities.KanbanColumn>(parameter, nameof(Core.Entities.KanbanColumn.Name), Name),
            ExpressionBuilder.BuildEqual<Core.Entities.KanbanColumn, Guid?>(parameter, nameof(Core.Entities.KanbanColumn.ProjectId), ProjectId),
            ExpressionBuilder.BuildEqual<Core.Entities.KanbanColumn, int?>(parameter, nameof(Core.Entities.KanbanColumn.Order), Order),
            ExpressionBuilder.BuildBetween<Core.Entities.KanbanColumn, int>(parameter, nameof(Core.Entities.KanbanColumn.Order), MinOrder, MaxOrder)
        ];
        
        if (!string.IsNullOrWhiteSpace(SearchTerm))
        {
            var nameSearch = ExpressionBuilder.BuildStringContains<Core.Entities.KanbanColumn>(
                parameter, nameof(Core.Entities.KanbanColumn.Name), SearchTerm, ignoreCase: true);
                
            predicates.Add(nameSearch);
        }

        var body = ExpressionBuilder.CombineWithAnd(predicates.ToArray());
        
        return Expression.Lambda<Func<Core.Entities.KanbanColumn, bool>>(body, parameter);
    }
}