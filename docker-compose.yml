services:
  backend:
    build: .
    container_name: virtumanager_backend
    ports:
      - "5173:5173"
    depends_on:
      mysql_db:
        condition: service_healthy
      mongodb:
        condition: service_healthy
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ConnectionStrings__DefaultConnection=server=mysql_db;uid=virtumanager_user;pwd=*****;database=virtumanager_db
      - MongoDb__ConnectionString=******************************************************************************************************************************
    networks:
      - app_network

  mysql_db:
    image: mysql:8.0
    container_name: virtumanager_mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: *****
      MYSQL_DATABASE: virtumanager_db
      MYSQL_USER: virtumanager_user
      MYSQL_PASSWORD: *****
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - app_network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "virtumanager_user", "--password=*****"]
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s

  mongodb:
    image: mongo:latest
    container_name: virtumanager_mongodb
    environment:
      MONGO_INITDB_ROOT_USERNAME: root
      MONGO_INITDB_ROOT_PASSWORD: rootpassword
      MONGO_INITDB_DATABASE: virtumanager_logs
    ports:
      - "27018:27017"
    volumes:
      - ./init-mongo.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
      - mongodb_data:/data/db
    command: mongod --auth
    networks:
      - app_network
    healthcheck:
      test: echo 'db.runCommand("ping").ok' | mongosh localhost:27017/test --quiet
      interval: 5s
      timeout: 5s
      retries: 5
      start_period: 10s

volumes:
  mysql_data:
  mongodb_data:

networks:
  app_network:
    driver: bridge
