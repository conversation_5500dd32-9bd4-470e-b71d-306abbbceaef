using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using VirtuManagerBackend.Application.Api.Dto.Node;
using VirtuManagerBackend.Application.Api.Requests;
using VirtuManagerBackend.Application.Api.Response.Node;
using VirtuManagerBackend.Application.Api.Response.PaginationResponse;
using VirtuManagerBackend.Application.Features.Nodes.Commands.AddNode;
using VirtuManagerBackend.Application.Features.Nodes.Commands.DeleteNode;
using VirtuManagerBackend.Application.Features.Nodes.Commands.InitNodeService;
using VirtuManagerBackend.Application.Features.Nodes.Commands.UpdateNode;
using VirtuManagerBackend.Domain.Db.Tables.NodeTable;
using VirtuManagerBackend.Domain.Db.Tables.UserTable;
using VirtuManagerBackend.Features.Nodes.Commands.GetNodeById;
using VirtuManagerBackend.Features.Nodes.Commands.GetNodesPage;
using VirtuManagerBackend.Presentation.Controllers.Base;

namespace VirtuManagerBackend.Presentation.Controllers;

[ApiController]
[Route("/Api/[controller]")]
public class NodesController : MediatorControllerBase
{
    public NodesController(IMediator mediator) : base(mediator) { }

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet("GetNodeById/{id}")]
    public async Task<IActionResult> GetNodeById(int id)
        => await ExecuteCommand<GetNodeByIdCommand, Node>(new GetNodeByIdCommand(id));

    [HttpPost("InitNodeService")]
    public async Task<IActionResult> InitNodeService(InitNodeRequest request)
        => await ExecuteCommand<InitNodeServiceCommand, NodeServiceInitResponse>(new InitNodeServiceCommand(request));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpPost("AddNode")]
    public async Task<IActionResult> AddNode(AddNodeDto nodeDto)
        => await ExecuteCommand<AddNodeCommand, AddNodeResponse>(new AddNodeCommand(nodeDto));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpDelete("DeleteNode")]
    public async Task<IActionResult> DeleteNode(IdRequestBase request)
        => await ExecuteCommand<DeleteNodeCommand, DeleteNodeResponse>(new DeleteNodeCommand(request.Id));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpPatch("UpdateNode")]
    public async Task<IActionResult> UpdateNode(Node node)
        => await ExecuteCommand<UpdateNodeCommand, UpdateNodeResponse>(new UpdateNodeCommand(node));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet("GetNodesPage")]
    public async Task<IActionResult> GetNodesPage(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
        => await ExecuteCommand<GetNodesPageCommand, PaginationResponse<Node>>(
            new GetNodesPageCommand(pageNumber, pageSize));
}