using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using VirtuManagerBackend.Application.Api.Dto.VirtualMachine;
using VirtuManagerBackend.Application.Api.Requests;
using VirtuManagerBackend.Application.Api.Response.PaginationResponse;
using VirtuManagerBackend.Application.Api.Response.VirtualMachine;
using VirtuManagerBackend.Application.Features.VirtualMachines.Commands.AddVirtualMachine;
using VirtuManagerBackend.Application.Features.VirtualMachines.Commands.DeleteVirtualMachine;
using VirtuManagerBackend.Application.Features.VirtualMachines.Commands.ForceStopVirtualMachine;
using VirtuManagerBackend.Application.Features.VirtualMachines.Commands.GetActiveVirtualMachinesCount;
using VirtuManagerBackend.Application.Features.VirtualMachines.Commands.GetShutVirtualMachinesCount;
using VirtuManagerBackend.Application.Features.VirtualMachines.Commands.GetVirtualMachineById;
using VirtuManagerBackend.Application.Features.VirtualMachines.Commands.GetVirtualMachinesCount;
using VirtuManagerBackend.Application.Features.VirtualMachines.Commands.GetVirtualMachinesPage;
using VirtuManagerBackend.Application.Features.VirtualMachines.Commands.StartVirtualMachine;
using VirtuManagerBackend.Application.Features.VirtualMachines.Commands.StopVirtualMachine;
using VirtuManagerBackend.Domain.Db.Tables.UserTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTable;
using VirtuManagerBackend.Presentation.Controllers.Base;

namespace VirtuManagerBackend.Presentation.Controllers;

[ApiController]
[Route("/Api/[controller]")]
public class VirtualMachinesController : MediatorControllerBase
{
    public VirtualMachinesController(IMediator mediator) : base(mediator) { }

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet("GetVirtualMachineById/{id}")]
    public async Task<IActionResult> GetVirtualMachineById(int id)
        => await ExecuteCommand<GetVirtualMachineByIdCommand, VirtualMachine>(new GetVirtualMachineByIdCommand(id));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpPost("AddVirtualMachine")]
    public async Task<IActionResult> AddVirtualMachine(AddVirtualMachineDto virtualMachineDto)
        => await ExecuteCommand<AddVirtualMachineCommand, AddVirtualMachineResponse>(
            new AddVirtualMachineCommand(virtualMachineDto));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpDelete("DeleteVirtualMachine")]
    public async Task<IActionResult> DeleteVirtualMachine(IdRequestBase request)
        => await ExecuteCommand<DeleteVirtualMachineCommand, DeleteVirtualMachineResponse>(
            new DeleteVirtualMachineCommand(request));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpPost("StartVirtualMachine")]
    public async Task<IActionResult> StartVirtualMachine(IdRequestBase request)
        => await ExecuteCommand<StartVirtualMachineCommand, StartVirtualMachineResponse>(
            new StartVirtualMachineCommand(request));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpPost("StopVirtualMachine")]
    public async Task<IActionResult> StopVirtualMachine(IdRequestBase request)
        => await ExecuteCommand<StopVirtualMachineCommand, StopVirtualMachineResponse>(
            new StopVirtualMachineCommand(request));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpPost("ForceStopVirtualMachine")]
    public async Task<IActionResult> ForceStopVirtualMachine(IdRequestBase request)
        => await ExecuteCommand<ForceStopVirtualMachineCommand, ForceStopVirtualMachineResponse>(
            new ForceStopVirtualMachineCommand(request));

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet("GetActiveVirtualMachinesCount")]
    public async Task<IActionResult> GetActiveVirtualMachinesCount()
        => await ExecuteCommand<GetActiveVirtualMachinesCountCommand, GetVirtualMachinesCountResponse>(
            new GetActiveVirtualMachinesCountCommand());

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet("GetVirtualMachinesCount")]
    public async Task<IActionResult> GetVirtualMachinesCount()
        => await ExecuteCommand<GetVirtualMachinesCountCommand, GetVirtualMachinesCountResponse>(
            new GetVirtualMachinesCountCommand());

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet("GetShutVirtualMachinesCount")]
    public async Task<IActionResult> GetShutVirtualMachinesCount()
        => await ExecuteCommand<GetShutVirtualMachinesCountCommand, GetVirtualMachinesCountResponse>(
            new GetShutVirtualMachinesCountCommand());

    [Authorize(Roles = nameof(UserRole.Admin))]
    [HttpGet("GetVirtualMachinesPage")]
    public async Task<IActionResult> GetVirtualMachinesPage(
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10)
        => await ExecuteCommand<GetVirtualMachinesPageCommand, PaginationResponse<VirtualMachine>>(
            new GetVirtualMachinesPageCommand(pageNumber, pageSize));
}