using System.ComponentModel.DataAnnotations;
using VirtuManager.Auth.Domain.Enums;

namespace VirtuManager.Auth.Domain.Entities;

public class User
{
    public Guid Id { get; set; }
    
    public string Login { get; set; }
    
    public string Email { get; set; }
    
    public string PasswordHash { get; set; }
    
    public string? RefreshTokenHash { get; set; }
    
    [EnumDataType(typeof(UserRole))]
    public UserRole Role { get; set; }
    
    public DateTime CreatedAt { get; set; }
    
    public DateTime? UpdatedAt { get; set; }
    
    public DateTime? LastLoginAt { get; set; }
}