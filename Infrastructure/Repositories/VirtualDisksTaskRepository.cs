using Microsoft.EntityFrameworkCore;
using VirtuManagerBackend.Domain.Db;
using VirtuManagerBackend.Domain.Db.Tables;
using VirtuManagerBackend.Domain.Db.Tables.VirtualDiskTaskTable;

namespace VirtuManagerBackend.Infrastructure.Repositories;

public class VirtualDisksTaskRepository : AbstractRepository<VirtualDiskTask>
{
    public VirtualDisksTaskRepository(ApplicationDbContext applicationDbContext) : base(applicationDbContext)
    {
    }

    public override async Task Add(VirtualDiskTask entity)
    {
        await ApplicationDbContext.VirtualDiskTasks.AddAsync(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public override async Task<List<VirtualDiskTask>> GetAll() =>
        await ApplicationDbContext.VirtualDiskTasks.ToListAsync();

    public override async Task Remove(VirtualDiskTask entity)
    {
        ApplicationDbContext.VirtualDiskTasks.Remove(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public override async Task Update(VirtualDiskTask entity)
    {
        ApplicationDbContext.VirtualDiskTasks.Update(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }
    
    public async Task<VirtualDiskTask?> GetTaskById(int id) => await ApplicationDbContext.VirtualDiskTasks.FirstOrDefaultAsync(vdt => vdt.Id == id);

    public async Task<IEnumerable<VirtualDiskTask>> GetPendingTasks() =>
        await ApplicationDbContext.VirtualDiskTasks
            .Where(t => t.Status == TaskState.Pending)
            .ToListAsync();
}