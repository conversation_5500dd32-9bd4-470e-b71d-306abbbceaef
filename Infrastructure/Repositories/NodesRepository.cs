using Microsoft.EntityFrameworkCore;
using VirtuManagerBackend.Domain.Db;
using VirtuManagerBackend.Domain.Db.Tables.NodeTable;

namespace VirtuManagerBackend.Infrastructure.Repositories;

public class NodesRepository : AbstractRepository<Node>
{
    public NodesRepository(ApplicationDbContext applicationDbContext) : base(applicationDbContext) { }

    public override async Task Add(Node entity)
    {
        await ApplicationDbContext.Nodes.AddAsync(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public override async Task<List<Node>> GetAll() => 
        await ApplicationDbContext.Nodes.ToListAsync();

    public override async Task Remove(Node entity)
    {
        ApplicationDbContext.Nodes.Remove(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public override async Task Update(Node entity)
    {
        ApplicationDbContext.Nodes.Update(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }
    
    public async Task<Node?> GetNodeById(int id) => await ApplicationDbContext.Nodes.FirstOrDefaultAsync(n => n.Id == id);

    public async Task<Node?> GetNodeByPrivateKey(string privateKey) =>
        await ApplicationDbContext.Nodes.FirstOrDefaultAsync(n => n.PrivateKey == privateKey);
    
    public async Task<Node?> GetNodeByTitle(string title) => await ApplicationDbContext.Nodes.FirstOrDefaultAsync(n => n.Title == title);

    public async Task<(IEnumerable<Node>? nodes, long totalCount)> GetNodesPage(int pageNumber, int pageSize)
        => await GetPaged(pageNumber, pageSize, ApplicationDbContext.Nodes.AsQueryable());
}