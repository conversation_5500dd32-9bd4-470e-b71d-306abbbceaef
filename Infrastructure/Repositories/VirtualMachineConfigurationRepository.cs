using Microsoft.EntityFrameworkCore;
using VirtuManagerBackend.Domain.Db;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineConfigurationTable;

namespace VirtuManagerBackend.Infrastructure.Repositories;

public class VirtualMachineConfigurationRepository : AbstractRepository<VirtualMachineConfiguration>
{
    public VirtualMachineConfigurationRepository(ApplicationDbContext applicationDbContext) : base(applicationDbContext)
    {
    }

    public override async Task Add(VirtualMachineConfiguration entity)
    {
        await ApplicationDbContext.VirtualMachineConfigurations.AddAsync(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public override async Task<List<VirtualMachineConfiguration>> GetAll()
        => await ApplicationDbContext.VirtualMachineConfigurations.ToListAsync();

    public override async Task Remove(VirtualMachineConfiguration entity)
    {
        ApplicationDbContext.VirtualMachineConfigurations.Remove(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public override async Task Update(VirtualMachineConfiguration entity)
    {
        ApplicationDbContext.VirtualMachineConfigurations.Update(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public async Task<VirtualMachineConfiguration?> GetConfigurationById(int id)
        => await ApplicationDbContext.VirtualMachineConfigurations.FirstOrDefaultAsync(c => c.Id == id);

    public async Task<VirtualMachineConfiguration?> GetConfigurationByTitle(string title)
        => await ApplicationDbContext.VirtualMachineConfigurations.FirstOrDefaultAsync(c => c.Title == title);

    public async Task<(IEnumerable<VirtualMachineConfiguration>? virtualMachineConfigurations, long totalCount)>
        GetConfigurationPage(int pageNumber, int pageSize)
        => await GetPaged(pageNumber, pageSize, ApplicationDbContext.VirtualMachineConfigurations.AsQueryable());
}