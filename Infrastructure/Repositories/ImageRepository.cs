using Microsoft.EntityFrameworkCore;
using VirtuManagerBackend.Domain.Db;
using VirtuManagerBackend.Domain.Db.Tables.ImageRepositoryTable;

namespace VirtuManagerBackend.Infrastructure.Repositories;

public class ImageRepository : AbstractRepository<RemoteImageRepository>
{
    public ImageRepository(ApplicationDbContext applicationDbContext) : base(applicationDbContext)
    {
    }

    public override async Task Add(RemoteImageRepository entity)
    {
        await ApplicationDbContext.ImageRepositories.AddAsync(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public override async Task<List<RemoteImageRepository>> GetAll()
        => await ApplicationDbContext.ImageRepositories.ToListAsync();

    public override async Task Remove(RemoteImageRepository entity)
    {
        ApplicationDbContext.ImageRepositories.Remove(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public override async Task Update(RemoteImageRepository entity)
    {
        ApplicationDbContext.ImageRepositories.Update(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public async Task<RemoteImageRepository?> GetRepositoryById(int id)
        => await ApplicationDbContext.ImageRepositories.FirstOrDefaultAsync(r => r.Id == id);

    public async Task<RemoteImageRepository?> GetRepositoryByDomain(string domain)
        => await ApplicationDbContext.ImageRepositories.FirstOrDefaultAsync(r => r.Domain == domain);

    public async Task<(IEnumerable<RemoteImageRepository>? imageRepositories, long totalCount)> GetImageRepositoriesPage(
        int pageNumber, int pageSize)
        => await GetPaged(pageNumber, pageSize, ApplicationDbContext.ImageRepositories.AsQueryable());
}