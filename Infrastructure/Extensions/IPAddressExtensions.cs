using System.Net;

namespace VirtuManagerBackend.Infrastructure.Extensions;

public static class IPAddressExtensions
{
    public static IEnumerable<string> GenerateIpAddresses(this IPAddress address, string network, string mask, string dhcpStart, string dhcpEnd)
    {
        var startIp = IPAddress.Parse(dhcpStart);
        var endIp = IPAddress.Parse(dhcpEnd);
        var current = startIp;

        while (address.CompareIpAddresses(current, endIp) <= 0)
        {
            yield return current.ToString();
            current = address.IncrementIpAddress(current);
        }
    }
    
    private static IPAddress IncrementIpAddress(this IPAddress ipAddress, IPAddress address)
    {
        var addressBytes = address.GetAddressBytes();
        for (var i = addressBytes.Length - 1; i >= 0; i--)
        {
            if (addressBytes[i] == 255)
            {
                addressBytes[i] = 0;
            }
            else
            {
                addressBytes[i]++;
                break;
            }
        }
        return new IPAddress(addressBytes);
    }
    
    public static int CompareIpAddresses(this IPAddress address, IPAddress address1, IPAddress address2)
    {
        byte[] bytes1 = address1.GetAddressBytes();
        byte[] bytes2 = address2.GetAddressBytes();

        for (int i = 0; i < bytes1.Length; i++)
        {
            if (bytes1[i] < bytes2[i])
                return -1;
            else if (bytes1[i] > bytes2[i])
                return 1;
        }

        return 0;
    }
}