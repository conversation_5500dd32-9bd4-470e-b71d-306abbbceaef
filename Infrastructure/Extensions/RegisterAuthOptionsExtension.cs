using VirtuManagerBackend.Authorization;

namespace VirtuManagerBackend.Infrastructure.Extensions;

public static class RegisterAuthOptionsExtension
{
    public static IServiceCollection AddAuthOptions(this IServiceCollection services, IConfiguration configuration)
    {
        var audience = configuration.GetSection("Auth").GetValue<string>("Audience");
        ArgumentNullException.ThrowIfNull(audience);
        
        var issuer = configuration.GetSection("Auth").GetValue<string>("Issuer");
        ArgumentNullException.ThrowIfNull(issuer);
        
        var key = configuration.GetSection("Auth").GetValue<string>("Key");
        ArgumentNullException.ThrowIfNull(key);
        
        services.AddSingleton(new AuthOptions()
        {
            Audience = audience,
            Issuer = issuer,
            Key = key
        });
        
        return services;
    }
}