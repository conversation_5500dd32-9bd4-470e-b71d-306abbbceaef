using System.Net;
using System.Net.NetworkInformation;
using VirtuManagerBackend.Domain.Db.Tables.ImageRepositoryTable;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Interfaces;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Infrastructure.BackgroundServices;

public class CheckImageServiceAvailableBackgroundService : BackgroundService, IAutoRegisterHostedService
{
    private readonly IServiceScopeFactory _serviceScopeFactory;
    
    public CheckImageServiceAvailableBackgroundService(IServiceScopeFactory scopeFactory)
    {
        ArgumentNullException.ThrowIfNull(scopeFactory);
        _serviceScopeFactory = scopeFactory;
    }
    
    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            await CheckImageServices();
            await Task.Delay(10000, stoppingToken);
        }
    }

    private async Task CheckImageServices()
    {
        var scope = _serviceScopeFactory.CreateScope();

        var imageRepository = scope.ServiceProvider.GetRequiredService<ImageRepository>();

        var repositories = await imageRepository.GetAll();
        await using var transaction = await imageRepository.BeginTransactionAsync();

        foreach (var repository in repositories)
        {
            await ProcessImageServiceServerPing(repository);
            await ProcessImageServiceAlive(repository);

            await imageRepository.Update(repository);
        }

        await transaction.CommitAsync();
    }

    private async Task ProcessImageServiceServerPing(RemoteImageRepository image)
    {
        try
        {
            var ping = new Ping();

            if (Uri.TryCreate(image.Domain, UriKind.Absolute, out var result))
            {
                var pingReply = await ping.SendPingAsync(IPAddress.Parse(result.Host));
                image.ServerAvailable = pingReply.Status == IPStatus.Success;
            }
            else
            {
                throw new Exception($"Error parse image service domain: {image.Domain}");
            }
        }
        catch (Exception e)
        {
            image.ServerAvailable = false;
        }
    }

    private async Task ProcessImageServiceAlive(RemoteImageRepository image)
    {
        try
        {
            var client = new HttpClient();
            var response = await client.GetAsync(image.Domain + "Api/ImageService/IsImageService");

            image.ServiceAvailable = response.StatusCode == HttpStatusCode.OK;
        }
        catch (Exception e)
        {
            image.ServiceAvailable = false;
        }
    }
}