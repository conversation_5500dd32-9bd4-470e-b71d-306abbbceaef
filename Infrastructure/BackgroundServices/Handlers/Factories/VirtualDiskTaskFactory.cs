using VirtuManagerBackend.Domain.Db.Tables.VirtualDiskTaskTable;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.Interfaces;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.VirtualDiskTaskHandlers;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.VirtualMachineTaskHandlers;

namespace VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.Factories;

public class VirtualDiskTaskFactory : ITaskFactory<VirtualDiskTask, TaskType>
{
    private readonly IServiceProvider _serviceProvider;
    
    public VirtualDiskTaskFactory(IServiceScopeFactory serviceScopeFactory)
    {
        ArgumentNullException.ThrowIfNull(serviceScopeFactory);
        
        _serviceProvider = serviceScopeFactory.CreateScope().ServiceProvider;
    }
    
    public ITaskHandler<VirtualDiskTask> GetHandler(TaskType type, VirtualDiskTask task)
    {
        switch (type)
        {
            case TaskType.AddDisk:
                var createVirtualDiskTaskHandler = _serviceProvider.GetRequiredService<CreateVirtualDiskHandler>();
                createVirtualDiskTaskHandler.SetTask(task);
                return createVirtualDiskTaskHandler;
            case TaskType.AttachDisk:
                var attachVirtualDiskTaskHandler =
                    _serviceProvider.GetRequiredService<AttachVirtualDiskToVirtualMachineTaskHandler>();
                attachVirtualDiskTaskHandler.SetTask(task);
                return attachVirtualDiskTaskHandler;
            case TaskType.DetachDisk:
                var detachVirtualDiskTaskHandler =
                    _serviceProvider.GetRequiredService<DetachVirtualDiskFromVirtualMachineTaskHandler>();
                detachVirtualDiskTaskHandler.SetTask(task);
                return detachVirtualDiskTaskHandler;
            case TaskType.CreateAndAttachDisk:
                var createAndAttachVirtualDiskTaskHandler =
                    _serviceProvider.GetRequiredService<CreateAndAttachVirtualDiskToVirtualMachineTaskHandler>();
                createAndAttachVirtualDiskTaskHandler.SetTask(task);
                return createAndAttachVirtualDiskTaskHandler;
        }

        throw new ArgumentOutOfRangeException();
    }
}