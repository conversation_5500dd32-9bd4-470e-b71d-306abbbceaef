using Grpc.Net.Client;
using virtu_manager_node_service;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTable;
using VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.Interfaces;
using VirtuManagerBackend.Infrastructure.Repositories;

namespace VirtuManagerBackend.Infrastructure.BackgroundServices.Handlers.UpdateStatsHandlers;

public class UpdateDiskStatHandler : IUpdateStatsHandler<VirtualMachine>
{
    private readonly VirtualDisksRepository _virtualDisksRepository;
    
    private VirtualMachine? _virtualMachine;
    
    public UpdateDiskStatHandler(VirtualDisksRepository virtualDisksRepository)
    {
        _virtualDisksRepository = virtualDisksRepository;
    }
    
    public async Task HandleAsync()
    {
        await using var transaction = await _virtualDisksRepository.BeginTransactionAsync();
        try
        {
            var node = _virtualMachine.Node;
            
            using var grpcChannel = GrpcChannel.ForAddress($"http://{node.Ip}:5212");
            var client = new VirtualMachineStats.VirtualMachineStatsClient(grpcChannel);

            var response = await client.GetDisksLoadAsync(new VirtualMachineGetStatsRequest()
            {
                VmName = _virtualMachine.Title
            });

            var disks = _virtualMachine.VirtualMachineSettings.VirtualDisks;

            foreach (var diskResponse in response.DiskStats)
            {
                foreach (var disk in disks)
                {
                    if (!diskResponse.DiskPath.Contains(disk.Name)) continue;
                    disk.AvailableSpace = diskResponse.Available;
                    disk.TotalSpace = diskResponse.Max;
                    disk.UsedSpace = diskResponse.Used;

                    await _virtualDisksRepository.Update(disk);
                }
            }
        }
        catch (Exception exception)
        {
            await transaction.RollbackAsync();
            return;
        }

        await transaction.CommitAsync();
    }

    public void SetEntity(VirtualMachine entity)
    {
        ArgumentNullException.ThrowIfNull(entity);
        
        _virtualMachine = entity;
    }
}