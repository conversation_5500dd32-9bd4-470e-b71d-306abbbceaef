using System.Linq.Expressions;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Identity.Core.Enums;
using TeamFlow.Shared.Contracts.Filtering;
using TeamFlow.Shared.Utils;
using UserRole = TeamFlow.Shared.Contracts.Enums.UserRole;

namespace TeamFlow.Identity.Application.Contracts.Filtering.Users;

public record UserFilterCriteria(
    string? Login,
    string? Email,
    UserOnlineStatus? OnlineStatus,
    UserRole? Role,
    Guid? PositionId,
    string? SearchTerm) : IFilterCriteria<User>
{
    public string? SearchTerm { get; } = SearchTerm;

    public Expression<Func<User, bool>>? ToPredicate()
    {
        var parameter = Expression.Parameter(typeof(Position), "u");

        List<Expression?> predicates =
        [
            ExpressionBuilder.BuildStringContains<User>(parameter, nameof(User.Login), Login, true),
            ExpressionBuilder.BuildStringContains<User>(parameter, nameof(User.Email), Email, true),
            ExpressionBuilder.BuildEqual<User, UserOnlineStatus?>(parameter, nameof(User.OnlineStatus), OnlineStatus),
            ExpressionBuilder.BuildEqual<User, UserRole?>(parameter, nameof(User.Role), Role),
            ExpressionBuilder.BuildEqual<User, Guid?>(parameter, nameof(User.PositionId), PositionId),
        ];

        if (!string.IsNullOrWhiteSpace(SearchTerm))
        {
            var nameSearch =
                ExpressionBuilder.BuildStringContains<Position>(parameter, nameof(Position.Name), Login, true);

            predicates.Add(nameSearch);
        }

        var body = ExpressionBuilder.CombineWithAnd(predicates.ToArray());

        return Expression.Lambda<Func<User, bool>>(body, parameter);
    }
}