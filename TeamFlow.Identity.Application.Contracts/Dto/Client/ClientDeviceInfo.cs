namespace TeamFlow.Identity.Application.Contracts.Dto.Client;

public class ClientDeviceInfo
{
    public string? DeviceType { get; set; } // mobile, desktop, tablet
    public string? DeviceName { get; set; } // "iPhone 15", "Chrome on Windows"
    public string? OsName { get; set; } // iOS, Windows, Android, macOS
    public string? OsVersion { get; set; } // 17.0, Windows 11
    public string? BrowserName { get; set; } // Chrome, Safari, Firefox
    public string? BrowserVersion { get; set; } // 120
    public string? TimeZone { get; set; } // Опционально для определения локации
}