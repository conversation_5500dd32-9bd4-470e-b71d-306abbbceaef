using TeamFlow.Identity.Core.Enums;
using UserRole = TeamFlow.Shared.Contracts.Enums.UserRole;

namespace TeamFlow.Identity.Application.Contracts.Dto.User;

public record UpdateUserRequest
{
    public string? Login { get; init; }

    public string? FirstName { get; init; }

    public string? LastName { get; init; }

    public string? Email { get; init; }

    public string? Password { get; init; }

    public UserRole? Role { get; init; }

    public Guid? PositionId { get; init; }
}