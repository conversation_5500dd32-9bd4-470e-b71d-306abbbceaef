using TeamFlow.Identity.Core.Enums.Settings;
using TeamFlow.Shared.Repositories.Entities;

namespace TeamFlow.Identity.Core.Entities;

public class UserSettings
{
    public bool EmailNotification { get; set; } = false;

    public bool SystemNotification { get; set; } = false;

    public bool EventReminder { get; set; } = false;

    public SidebarPanelMode PanelMode { get; set; } = SidebarPanelMode.Extended;
    
    public UiTheme UiTheme { get; set; } = UiTheme.System;

    public Language Language { get; set; } = Language.Ru;
    
    public required Guid UserId { get; set; }
    
    public User User { get; set; }
}