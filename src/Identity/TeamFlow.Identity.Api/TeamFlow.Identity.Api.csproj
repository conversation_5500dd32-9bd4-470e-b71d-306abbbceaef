<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Elastic.Serilog.Sinks" Version="8.18.1" />
        <PackageReference Include="FluentResults.Extensions.AspNetCore" Version="0.1.0" />
        <PackageReference Include="Grpc.AspNetCore" Version="2.70.0-pre1" />
        <PackageReference Include="Grpc.Tools" Version="2.70.0">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="MediatR.Extensions.FluentValidation.AspNetCore" Version="5.1.0" />
        <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.2" />
        <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.1"/>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.2">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.4" />
        <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="9.0.0" />
        <PackageReference Include="Serilog" Version="4.3.0" />
        <PackageReference Include="Serilog.AspNetCore" Version="9.0.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="7.2.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerGen" Version="7.2.0" />
        <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="7.2.0" />
    </ItemGroup>

    <ItemGroup>
      <Content Include="..\.dockerignore">
        <Link>.dockerignore</Link>
      </Content>
    </ItemGroup>
    
    <ItemGroup>
        <Protobuf Include="Protos\position.proto" GrpcServices="Server"/>
        <Protobuf Include="Protos\user.proto" GrpcServices="Server"/>
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\TeamFlow.Identity.Application\TeamFlow.Identity.Application.csproj" />
      <ProjectReference Include="..\TeamFlow.Identity.Infrastructure\TeamFlow.Identity.Infrastructure.csproj" />
      <ProjectReference Include="..\TeamFlow.Identity.Persistence\TeamFlow.Identity.Persistence.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Services\" />
    </ItemGroup>

</Project>
