using FluentResults.Extensions.AspNetCore;
using MediatR;
using TeamFlow.Identity.Core.Errors;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using TeamFlow.Identity.Api.Utils.Interfaces;
using TeamFlow.Identity.Application.Contracts.Features.Commands.Auth;

namespace TeamFlow.Identity.Api.Controllers;

[ApiController]
[Authorize]
[Route("/api/[controller]")]
public sealed class AuthController : ControllerBase
{
    private readonly ISender _sender;
    private readonly ICookieUtils _cookieUtils;

    public AuthController(ISender sender, ICookieUtils cookieUtils)
    {
        ArgumentNullException.ThrowIfNull(sender);
        ArgumentNullException.ThrowIfNull(cookieUtils);

        _sender = sender;
        _cookieUtils = cookieUtils;
    }

    [AllowAnonymous]
    [HttpPost("/login")]
    public async Task<IActionResult> Login(
        [FromBody] AuthenticateCommand request,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(request, cancellationToken);

        if (result.IsFailed)
        {
            result.ToActionResult();
        }

        _cookieUtils.SetRefreshTokenCookie(Response, result.Value.RefreshToken);

        return Ok(result.Value.AccessToken);
    }

    [HttpPost(nameof(Refresh))]
    public async Task<IActionResult> Refresh(CancellationToken cancellationToken = default)
    {
        var refreshToken = _cookieUtils.GetRefreshTokenFromCookie(Request);
        if (refreshToken is null)
        {
            return Unauthorized(AuthErrors.InvalidRefreshToken);
        }

        var result = await _sender.Send(new RefreshTokenCommand(refreshToken), cancellationToken);

        if (result.IsFailed)
        {
            return result.ToActionResult();
        }

        _cookieUtils.SetRefreshTokenCookie(Response, result.Value.RefreshToken);

        return Ok(result.Value.AccessToken);
    }

    [HttpPost(nameof(Logout))]
    public async Task<IActionResult> Logout(CancellationToken cancellationToken = default)
    {
        var refreshToken = _cookieUtils.GetRefreshTokenFromCookie(Request);
        if (refreshToken is null) return LogoutSuccessfully();

        var result = await _sender.Send(new LogoutCommand(refreshToken), cancellationToken);

        if (result.IsFailed)
        {
            LogoutSuccessfully();
        }

        return LogoutSuccessfully();
    }

    private IActionResult LogoutSuccessfully()
    {
        Response.Headers.Remove("Authorization");
        _cookieUtils.RemoveRefreshTokenFromCookie(Response);
        return Ok("Logged out successfully");
    }
}