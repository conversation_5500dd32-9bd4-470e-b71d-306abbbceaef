syntax = "proto3";

option csharp_namespace = "VanId";

package Grpc.Position;

service GrpcPositionService {
  rpc GetPositionByGuid(GetPositionByGuidRequest) returns (Position);
  rpc GetPositionByName(GetPositionByNameRequest) returns (Position);
  rpc GetPositionsPaged(GetPositionsPagedRequest) returns (GetPositionsPagedResponse);
  rpc GetAllPositions(GetAllPositionsRequest) returns (GetAllPositionsResponse);
}

message GetPositionsPagedRequest {
  int32 pageNumber = 1;
  int32 pageSize = 2;
}

message GetPositionByGuidRequest {
  string uuid = 1;
}

message GetPositionByNameRequest {
  string name = 1;
}

message GetAllPositionsRequest {}

message GetAllPositionsResponse {
  repeated Position positions = 1;
}

message GetPositionsPagedResponse {
  int64 totalCount = 1;
  double totalPages = 2;
  repeated Position positions = 3;
}

message Position {
  string id = 1;
  string name = 2;
}