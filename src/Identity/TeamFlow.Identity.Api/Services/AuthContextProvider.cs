using TeamFlow.Identity.Application.Contracts.Dto.Client;
using TeamFlow.Identity.Application.Contracts.Services;

namespace TeamFlow.Identity.Api.Services;

public class AuthContextService : IAuthContextService
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly ILogger<AuthContextService> _logger;
    //TODO Здесь можно добавить сервис для определения геолокации по IP

    public AuthContextService(IHttpContextAccessor httpContextAccessor, ILogger<AuthContextService> logger)
    {
        _httpContextAccessor = httpContextAccessor;
        _logger = logger;
    }

    public string GetClientIpAddress()
    {
        var context = _httpContextAccessor.HttpContext;
        if (context == null) return "Unknown";

        // Проверяем заголовки прокси
        var ipAddress = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();
        if (!string.IsNullOrEmpty(ipAddress))
        {
            return ipAddress.Split(',')[0].Trim();
        }

        ipAddress = context.Request.Headers["X-Real-IP"].FirstOrDefault();
        if (!string.IsNullOrEmpty(ipAddress))
        {
            return ipAddress;
        }

        // Fallback на RemoteIpAddress
        return context.Connection.RemoteIpAddress?.ToString() ?? "Unknown";
    }

    public string GetUserAgent()
    {
        var context = _httpContextAccessor.HttpContext;
        return context?.Request.Headers.UserAgent.ToString() ?? "Unknown";
    }

    //TODO need to implement
    public async Task<(string? Country, string? City)> GetLocationAsync(string ipAddress)
    {
        try
        {
            // Здесь интеграция с сервисом геолокации (MaxMind, IPStack, etc.)
            // Заглушка для примера
            return (null, null);
            // return ("RU", "Moscow");
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get location for IP: {IpAddress}", ipAddress);
            return (null, null);
        }
    }

    public ClientDeviceInfo ParseUserAgent(string userAgent)
    {
        // Простой парсинг User-Agent
        // В реальном проекте лучше использовать библиотеку типа UAParser.NET
        try
        {
            var deviceInfo = new ClientDeviceInfo();
            
            if (userAgent.Contains("Mobile"))
                deviceInfo.DeviceType = "mobile";
            else if (userAgent.Contains("Tablet"))
                deviceInfo.DeviceType = "tablet";
            else
                deviceInfo.DeviceType = "desktop";

            if (userAgent.Contains("Chrome"))
                deviceInfo.BrowserName = "Chrome";
            else if (userAgent.Contains("Firefox"))
                deviceInfo.BrowserName = "Firefox";
            else if (userAgent.Contains("Safari"))
                deviceInfo.BrowserName = "Safari";

            return deviceInfo;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to parse User-Agent: {UserAgent}", userAgent);
            return new ClientDeviceInfo();
        }
    }
}