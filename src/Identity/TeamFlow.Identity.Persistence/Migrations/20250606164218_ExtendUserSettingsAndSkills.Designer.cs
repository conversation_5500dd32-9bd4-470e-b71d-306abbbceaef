// <auto-generated />
using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;
using TeamFlow.Identity.Persistence.Database;

#nullable disable

namespace TeamFlow.Identity.Persistence.Migrations
{
    [DbContext(typeof(ApplicationDbContext))]
    [Migration("20250606164218_ExtendUserSettingsAndSkills")]
    partial class ExtendUserSettingsAndSkills
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.2")
                .HasAnnotation("Relational:MaxIdentifierLength", 63);

            NpgsqlModelBuilderExtensions.UseIdentityByDefaultColumns(modelBuilder);

            modelBuilder.Entity("TeamFlow.Identity.Core.Entities.Position", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Positions");

                    b.HasData(
                        new
                        {
                            Id = new Guid("11111111-1111-1111-1111-111111111111"),
                            CreatedAt = new DateTime(2025, 2, 21, 0, 0, 0, 0, DateTimeKind.Utc),
                            Name = "System Administrator",
                            UpdatedAt = new DateTime(2025, 6, 6, 16, 42, 18, 113, DateTimeKind.Utc).AddTicks(7385)
                        });
                });

            modelBuilder.Entity("TeamFlow.Identity.Core.Entities.Skill", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("Category")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("Category");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("Skill");

                    b.HasData(
                        new
                        {
                            Id = new Guid("01974584-eb10-7505-82a2-b11585b61a74"),
                            Category = "Admin",
                            CreatedAt = new DateTime(2025, 2, 21, 0, 0, 0, 0, DateTimeKind.Utc),
                            Name = "Admin",
                            UpdatedAt = new DateTime(2025, 6, 6, 16, 42, 18, 114, DateTimeKind.Utc).AddTicks(3271)
                        });
                });

            modelBuilder.Entity("TeamFlow.Identity.Core.Entities.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uuid");

                    b.Property<string>("AvatarUrl")
                        .HasColumnType("text");

                    b.Property<DateTime>("CreatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(255)
                        .HasColumnType("character varying(255)");

                    b.Property<DateTime?>("LastLoginAt")
                        .HasColumnType("timestamp with time zone");

                    b.Property<string>("Login")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("character varying(50)");

                    b.Property<int>("OnlineStatus")
                        .HasColumnType("integer");

                    b.Property<string>("PasswordHash")
                        .IsRequired()
                        .HasColumnType("text");

                    b.Property<Guid>("PositionId")
                        .HasColumnType("uuid");

                    b.Property<string>("RefreshTokenHash")
                        .HasColumnType("text");

                    b.Property<int>("Role")
                        .HasColumnType("integer");

                    b.Property<DateTime>("UpdatedAt")
                        .HasColumnType("timestamp with time zone");

                    b.HasKey("Id");

                    b.HasIndex("CreatedAt");

                    b.HasIndex("Email")
                        .IsUnique();

                    b.HasIndex("LastLoginAt");

                    b.HasIndex("Login")
                        .IsUnique();

                    b.HasIndex("PositionId");

                    b.HasIndex("Role");

                    b.ToTable("Users");

                    b.HasData(
                        new
                        {
                            Id = new Guid("*************-2222-2222-************"),
                            CreatedAt = new DateTime(2025, 2, 21, 0, 0, 0, 0, DateTimeKind.Utc),
                            Email = "<EMAIL>",
                            Login = "admin",
                            OnlineStatus = 0,
                            PasswordHash = "$2a$09$jIqGzmvhNyBGmqJgs.qcAuuntFF8jmK1sacrwbmvmLm4jwv0GITuC",
                            PositionId = new Guid("11111111-1111-1111-1111-111111111111"),
                            Role = 1,
                            UpdatedAt = new DateTime(2025, 6, 6, 16, 42, 18, 114, DateTimeKind.Utc).AddTicks(4349)
                        });
                });

            modelBuilder.Entity("TeamFlow.Identity.Core.Entities.UserSettings", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<bool>("EmailNotification")
                        .HasColumnType("boolean");

                    b.Property<bool>("EventReminder")
                        .HasColumnType("boolean");

                    b.Property<int>("Language")
                        .HasColumnType("integer");

                    b.Property<int>("PanelMode")
                        .HasColumnType("integer");

                    b.Property<bool>("SystemNotification")
                        .HasColumnType("boolean");

                    b.Property<int>("UiTheme")
                        .HasColumnType("integer");

                    b.HasKey("UserId");

                    b.HasIndex("UserId")
                        .IsUnique();

                    b.ToTable("UserSettings");

                    b.HasData(
                        new
                        {
                            UserId = new Guid("*************-2222-2222-************"),
                            EmailNotification = false,
                            EventReminder = false,
                            Language = 0,
                            PanelMode = 1,
                            SystemNotification = false,
                            UiTheme = 0
                        });
                });

            modelBuilder.Entity("TeamFlow.Identity.Core.Entities.UserSkill", b =>
                {
                    b.Property<Guid>("SkillId")
                        .HasColumnType("uuid");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uuid");

                    b.Property<bool>("IsPrimary")
                        .HasColumnType("boolean");

                    b.Property<int>("Level")
                        .HasColumnType("integer");

                    b.Property<int>("YearsExperience")
                        .HasColumnType("integer");

                    b.HasKey("SkillId", "UserId");

                    b.HasIndex("SkillId");

                    b.HasIndex("UserId");

                    b.HasIndex("YearsExperience");

                    b.HasIndex("SkillId", "Level");

                    b.HasIndex("UserId", "IsPrimary");

                    b.ToTable("UserSkill");

                    b.HasData(
                        new
                        {
                            SkillId = new Guid("01974584-eb10-7505-82a2-b11585b61a74"),
                            UserId = new Guid("*************-2222-2222-************"),
                            IsPrimary = true,
                            Level = 4,
                            YearsExperience = 10000
                        });
                });

            modelBuilder.Entity("TeamFlow.Identity.Core.Entities.User", b =>
                {
                    b.HasOne("TeamFlow.Identity.Core.Entities.Position", "Position")
                        .WithMany("Users")
                        .HasForeignKey("PositionId")
                        .OnDelete(DeleteBehavior.SetNull)
                        .IsRequired();

                    b.Navigation("Position");
                });

            modelBuilder.Entity("TeamFlow.Identity.Core.Entities.UserSettings", b =>
                {
                    b.HasOne("TeamFlow.Identity.Core.Entities.User", "User")
                        .WithOne("Settings")
                        .HasForeignKey("TeamFlow.Identity.Core.Entities.UserSettings", "UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("TeamFlow.Identity.Core.Entities.UserSkill", b =>
                {
                    b.HasOne("TeamFlow.Identity.Core.Entities.Skill", "Skill")
                        .WithMany()
                        .HasForeignKey("SkillId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("TeamFlow.Identity.Core.Entities.User", "User")
                        .WithMany("UserSkills")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Skill");

                    b.Navigation("User");
                });

            modelBuilder.Entity("TeamFlow.Identity.Core.Entities.Position", b =>
                {
                    b.Navigation("Users");
                });

            modelBuilder.Entity("TeamFlow.Identity.Core.Entities.User", b =>
                {
                    b.Navigation("Settings");

                    b.Navigation("UserSkills");
                });
#pragma warning restore 612, 618
        }
    }
}
