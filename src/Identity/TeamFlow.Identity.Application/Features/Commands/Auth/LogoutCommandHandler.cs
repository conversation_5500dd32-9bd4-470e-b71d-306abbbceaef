using FluentResults;
using FluentValidation;
using MediatR;
using TeamFlow.Identity.Application.Contracts.Features.Commands.Auth;
using TeamFlow.Identity.Application.Contracts.Services;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Identity.Core.Repositories;

namespace TeamFlow.Identity.Application.Features.Commands.Auth;

public sealed class LogoutCommandHandler : IRequestHandler<LogoutCommand, Result>
{
    private readonly IGenericRepository<User, Guid> _usersRepository;
    private readonly IRefreshTokenHelper _refreshTokenHelper;
    private readonly IValidator<LogoutCommand> _validator;

    public LogoutCommandHandler(IGenericRepository<User, Guid> usersRepository, IRefreshTokenHelper refreshTokenHelper, IValidator<LogoutCommand> validator)
    {
        _usersRepository = usersRepository;
        _refreshTokenHelper = refreshTokenHelper;
        _validator = validator;
    }

    public async Task<Result> Handle(LogoutCommand request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(User), validationResult));
        }
        
        var user = await _refreshTokenHelper.GetUserFromRefreshTokenAsync(request.RefreshToken, cancellationToken);

        if (user.IsFailed)
        {
            return Result.Fail(user.Errors);
        }
        
        user.Value.RefreshTokenHash = null;
        user.Value.UpdatedAt = DateTime.UtcNow;
        await _usersRepository.UpdateAsync(user.Value, cancellationToken);
        return Result.Ok();
    }
}