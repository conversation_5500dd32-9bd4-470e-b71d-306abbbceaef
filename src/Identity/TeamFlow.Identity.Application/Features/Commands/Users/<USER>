using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Identity.Application.Contracts.Features.Commands.User;
using TeamFlow.Identity.Application.Contracts.Services;
using TeamFlow.Identity.Application.Errors;
using TeamFlow.Identity.Application.Services.Interfaces;
using TeamFlow.Identity.Core.Entities;
using TeamFlow.Identity.Core.Repositories;

namespace TeamFlow.Identity.Application.Features.Commands.Users;

public class CreateUserCommandHandler : IRequestHandler<CreateUserCommand, Result<Guid>>
{
    private readonly IGenericRepository<User, Guid> _usersRepository;
    private readonly IValidator<CreateUserCommand> _validator;
    private readonly ILogger<CreateUserCommandHandler> _logger;
    private readonly IHashDataService _hashDataService;

    public CreateUserCommandHandler(IGenericRepository<User, Guid> usersRepository,
        IValidator<CreateUserCommand> validator, ILogger<CreateUserCommandHandler> logger,
        IHashDataService hashDataService)
    {
        _usersRepository = usersRepository;
        _validator = validator;
        _logger = logger;
        _hashDataService = hashDataService;
    }

    public async Task<Result<Guid>> Handle(CreateUserCommand request, CancellationToken cancellationToken)
    {
        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(User), validationResult));
        }
        
        var isUserExists = await _usersRepository.FindAnyAsync(
            u => u.Login == request.Login || u.Email == request.Email,
            cancellationToken);

        if (isUserExists)
        {
            return Result.Fail(ErrorsFactory.AlreadyExists(nameof(User), nameof(request.Login), request.Login));
        }

        var user = new User
        {
            Id = Guid.CreateVersion7(),
            Login = request.Login,
            Email = request.Email,
            FirstName = request.FirstName,
            LastName = request.LastName,
            PasswordHash = _hashDataService.HashData(request.Password),
            Role = request.Role,
            PositionId = request.PositionId,
            CreatedAt = DateTime.Now
        };

        await _usersRepository.AddAsync(user, cancellationToken);
        return user.Id;
    }
}