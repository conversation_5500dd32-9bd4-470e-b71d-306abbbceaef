using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.Extensions.Options;
using TeamFlow.Tasks.Api.Auth;

namespace TeamFlow.Tasks.Api.Extensions.Di;

public static class AuthenticationExtensions
{
    public static IServiceCollection AddJwtAuthentication(this IServiceCollection services,
        IConfiguration configuration)
    {
        // Регистрация AuthOptions из конфигурации
        services.Configure<AuthOptions>(configuration.GetSection("Auth"));
        services.AddSingleton(sp => sp.GetRequiredService<IOptions<AuthOptions>>().Value);
        
        // Регистрация AuthConfiguration
        services.AddSingleton<AuthConfiguration>();

        services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                // options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                using var serviceProvider = services.BuildServiceProvider();
                var authConfiguration = serviceProvider.GetService<AuthConfiguration>();

                options.TokenValidationParameters = authConfiguration!.GetTokenValidationParameters();
            });
        
        return services;
    }
}