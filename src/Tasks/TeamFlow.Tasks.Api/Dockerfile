FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS base
USER $APP_UID
WORKDIR /app
#EXPOSE 8080
#EXPOSE 8081
EXPOSE 5100
ENV ASPNETCORE_URLS=http://+:5100

FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["src/TeamFlow.Task.Api/TeamFlow.Task.Api.csproj", "src/TeamFlow.Task.Api/"]
COPY ["src/TeamFlow.Task.Application/TeamFlow.Task.Application.csproj", "src/TeamFlow.Task.Application/"]
COPY ["src/TeamFlow.Task.Core/TeamFlow.Task.Core.csproj", "src/TeamFlow.Task.Core/"]
COPY ["src/TeamFlow.Task.Infrastructure/TeamFlow.Task.Infrastructure.csproj", "src/TeamFlow.Task.Infrastructure/"]
RUN dotnet restore "src/TeamFlow.Task.Api/TeamFlow.Task.Api.csproj"
COPY . .
WORKDIR "/src/src/TeamFlow.Task.Api"
RUN dotnet build "TeamFlow.Task.Api.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "TeamFlow.Task.Api.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "TeamFlow.Task.Api.dll"]
