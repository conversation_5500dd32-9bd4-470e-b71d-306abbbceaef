using FluentResults.Extensions.AspNetCore;
using MediatR;
using Microsoft.AspNetCore.Mvc;
using TeamFlow.Shared.Contracts.Pagination;
using TeamFlow.Tasks.Api.Api.Filtering.Comments;
using TeamFlow.Tasks.Api.Api.Requests.Comments;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Commands;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Comment;

namespace TeamFlow.Tasks.Api.Controllers;

// [Authorize]
[Route("api/[controller]")]
[ApiController]
public class CommentController : ControllerBase
{
    private readonly ISender _sender;

    public CommentController(ISender sender)
    {
        _sender = sender;
    }

    [HttpGet("{id:guid}")]
    public async Task<IActionResult> GetById(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new GetCommentByIdQuery(id), cancellationToken);
        return result.ToActionResult();
    }

    [HttpGet]
    public async Task<IActionResult> GetManyAsync(
        [FromQuery] CommentFilteringQueryParameters filterParameters,
        [FromQuery] CommentSortingQueryParameters sortingParameters,
        [FromQuery] string? searchTerm = null,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        var pageable = new Pageable(pageNumber, pageSize);
        var filterCriteria = new CommentFilterCriteria(filterParameters.PublisherId, filterParameters.TaskId,
            filterParameters.MentionedUserId, filterParameters.ParentCommentId, filterParameters.CreatedAtFrom,
            filterParameters.CreatedAtTo, searchTerm);
        var sortingCriteria = new CommentSortingCriteria(sortingParameters.SortField, sortingParameters.Direction);

        var result = await _sender.Send(new GetCommentsQuery(pageable, filterCriteria, sortingCriteria), cancellationToken);

        return result.ToActionResult();
    }

    [HttpGet("publisher/{publisherId:guid}")]
    public async Task<IActionResult> GetByPublisherId(Guid publisherId, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new GetCommentsByPublisherIdQuery(publisherId), cancellationToken);
        return result.ToActionResult();
    }

    [HttpGet("publisher/{publisherId:guid}/paged")]
    public async Task<IActionResult> GetPagedByPublisherId(
        Guid publisherId,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        var pageable = new Pageable(pageNumber, pageSize);
        var result = await _sender.Send(new GetCommentsByPublisherIdPageableQuery(pageable, publisherId), cancellationToken);
        return result.ToActionResult();
    }

    [HttpGet("task/{taskId:guid}")]
    public async Task<IActionResult> GetByTaskId(Guid taskId, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new GetCommentsByTaskIdQuery(taskId), cancellationToken);
        return result.ToActionResult();
    }

    [HttpGet("task/{taskId:guid}/paged")]
    public async Task<IActionResult> GetPagedByTaskId(
        Guid taskId,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        var pageable = new Pageable(pageNumber, pageSize);
        var result = await _sender.Send(new GetCommentsByTaskIdPageableQuery(pageable, taskId), cancellationToken);
        return result.ToActionResult();
    }

    [HttpGet("parent/{parentCommentId:guid}")]
    public async Task<IActionResult> GetByParentCommentId(Guid parentCommentId,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new GetCommentsByParentCommentIdQuery(parentCommentId), cancellationToken);
        return result.ToActionResult();
    }

    [HttpGet("parent/{parentCommentId:guid}/paged")]
    public async Task<IActionResult> GetPagedByParentCommentId(
        Guid parentCommentId,
        [FromQuery] int pageNumber = 1,
        [FromQuery] int pageSize = 10,
        CancellationToken cancellationToken = default)
    {
        var pageable = new Pageable(pageNumber, pageSize);
        var result = await _sender.Send(new GetCommentsByParentCommentIdPageableQuery(pageable, parentCommentId), cancellationToken);
        return result.ToActionResult();
    }

    [HttpPost]
    public async Task<IActionResult> Create([FromBody] CreateCommentCommand request,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(request, cancellationToken);
        return result.ToActionResult();
    }

    [HttpPatch("{id:guid}")]
    public async Task<IActionResult> Update(Guid id, [FromBody] UpdateCommentRequest request,
        CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new UpdateCommentCommand(id, request.Content, request.MentionedUserIds), cancellationToken);
        return result.ToActionResult();
    }

    [HttpDelete("{id:guid}")]
    public async Task<IActionResult> Delete(Guid id, CancellationToken cancellationToken = default)
    {
        var result = await _sender.Send(new DeleteCommentCommand(id), cancellationToken);
        return result.ToActionResult();
    }
}