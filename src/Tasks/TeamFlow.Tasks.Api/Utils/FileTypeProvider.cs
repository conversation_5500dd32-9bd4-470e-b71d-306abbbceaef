using Microsoft.AspNetCore.StaticFiles;
using TeamFlow.Tasks.Core.Utils;

namespace TeamFlow.Tasks.Api.Utils;

public class FileTypeProvider : IFileTypeProvider
{
    private readonly FileExtensionContentTypeProvider _contentTypeProvider = new();
    
    

    public bool TryGetContentType(string fileName, out string? contentType)
    {
        return _contentTypeProvider.TryGetContentType(fileName, out contentType);
    }
}