using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Tasks.Api.Api.Filtering.Common;
using TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.Task;

namespace TeamFlow.Tasks.Api.Api.Filtering.Tasks;

public record TaskSortingQueryParameters(
    TaskSortField SortField = TaskSortField.CreatedAt,
    SortDirection Direction = SortDirection.Descending)
    : AbstractSortingQueryParameters<TaskSortField>(<PERSON><PERSON><PERSON><PERSON>, Direction);