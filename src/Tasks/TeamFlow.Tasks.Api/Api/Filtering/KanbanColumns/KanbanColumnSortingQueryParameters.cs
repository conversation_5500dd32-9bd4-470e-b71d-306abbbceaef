using TeamFlow.Shared.Contracts.Enums;
using TeamFlow.Tasks.Api.Api.Filtering.Common;
using TeamFlow.Tasks.Application.Contracts.Contracts.Filtering.KanbanColumn;

namespace TeamFlow.Tasks.Api.Api.Filtering.KanbanColumns;

public record KanbanColumnSortingQueryParameters(
    KanbanColumnSortField SortField = KanbanColumnSortField.CreatedAt,
    SortDirection Direction = SortDirection.Descending)
    : AbstractSortingQueryParameters<KanbanColumnSortField>(SortField, Direction);