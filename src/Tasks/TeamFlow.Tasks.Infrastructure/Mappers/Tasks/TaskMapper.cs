using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Infrastructure.Mappers.Tasks;

public class TaskMapper : IMapper<TaskViewModel, TaskEntity>
{
    private readonly TaskMappingProfile _mapper;

    public TaskMapper(TaskMappingProfile mapper)
    {
        ArgumentNullException.ThrowIfNull(mapper, nameof(mapper));
        _mapper = mapper;
    }

    public TaskViewModel MapToModel(TaskEntity source) => _mapper.MapToViewModel(source);

    public List<TaskViewModel> MapToModel(IEnumerable<TaskEntity> sources)
        => sources.Select(x => _mapper.MapToViewModel(x)).ToList();
}