using Riok.Mapperly.Abstractions;
using TeamFlow.Tasks.Core.Contracts.External;
using VanId;

namespace TeamFlow.Tasks.Infrastructure.Mappers.Users;

[Mapper]
public partial class UserMappingProfile
{
    [MapProperty(nameof(User.Id), nameof(UserDto.Id), Use = nameof(StringToGuid))]
    [MapProperty(nameof(User.PositionId), nameof(UserDto.PositionId), Use = nameof(StringToGuid))]
    [MapProperty(nameof(User.Role), nameof(UserDto.Role), Use = nameof(ConvertRole))]
    public partial UserDto MapToUserDto(User user);
    
    private static Guid StringToGuid(string value) => Guid.Parse(value);

    private static Shared.Contracts.Enums.UserRole ConvertRole(UserRole role) => (Shared.Contracts.Enums.UserRole)role;
}