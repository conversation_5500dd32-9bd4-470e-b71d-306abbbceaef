namespace TeamFlow.Tasks.Infrastructure.Caching;

public class TwoLevelCacheOptions
{
    /// <summary>
    /// Время жизни кеша первого уровня (in-memory) по умолчанию
    /// </summary>
    public TimeSpan DefaultLocalExpirationTime { get; set; } = TimeSpan.FromMinutes(5);

    /// <summary>
    /// Время жизни распределенного кеша по умолчанию
    /// </summary>
    public TimeSpan DefaultDistributedExpirationTime { get; set; } = TimeSpan.FromHours(1);

    /// <summary>
    /// Если true, то при отсутствии в локальном кеше и нахождении в распределенном, 
    /// значение будет добавлено и в локальный кеш
    /// </summary>
    public bool PopulateLocalCacheOnDistributedHit { get; set; } = true;

    /// <summary>
    /// Количество обращений к ключу, после которого он считается "горячим" и сохраняется в L1
    /// </summary>
    public int HotDataAccessThreshold { get; set; } = 3;

    /// <summary>
    /// Интервал времени для отслеживания обращений к ключу (в секундах)
    /// </summary>
    public int AccessTrackingIntervalSeconds { get; set; } = 60;
}