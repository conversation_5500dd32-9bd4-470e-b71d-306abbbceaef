using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Comment.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;
using TeamFlow.Tasks.Core.Entities;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Features.Comments.Commands;

public sealed class CreateCommentCommandHandler : IRequestHandler<CreateCommentCommand, Result<Guid>>
{
    private readonly IRepository<Comment, Guid> _commentsRepository;
    private readonly IRepository<TaskEntity, Guid> _tasksRepository;
    private readonly IExternalUserService _externalUserService;
    private readonly ILogger<CreateCommentCommandHandler> _logger;
    private readonly IValidator<CreateCommentCommand> _validator;

    public CreateCommentCommandHandler(
        IRepository<Comment, Guid> commentsRepository,
        IRepository<TaskEntity, Guid> tasksRepository, 
        ILogger<CreateCommentCommandHandler> logger,
        IValidator<CreateCommentCommand> validator, 
        IExternalUserService externalUserService)
    {
        _commentsRepository = commentsRepository;
        _tasksRepository = tasksRepository;
        _logger = logger;
        _validator = validator;
        _externalUserService = externalUserService;
    }

    public async Task<Result<Guid>> Handle(CreateCommentCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Создание комментария для задачи с ID {TaskId}", request.TaskId);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "создании комментария");
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Comment), validationResult));
        }

        var isTaskExists = await _tasksRepository.FindAnyAsync(x => x.Id == request.TaskId, cancellationToken);

        if (!isTaskExists)
        {
            _logger.LogWarning("Задача с ID {TaskId} не найдена при создании комментария", request.TaskId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(TaskEntity), request.TaskId));
        }

        //TODO Can be improved with bulk operations
        List<Guid> validIds = [];
        if (request.MentionedUserIds is not null)
        {
            foreach (var id in request.MentionedUserIds)
            {
                var userResult = await _externalUserService.GetByGuidAsync(id, cancellationToken);

                if (userResult.IsFailed)
                {
                    continue;
                }

                validIds.Add(id);
            }
        }

        var comment = new Comment
        {
            Id = Guid.CreateVersion7(),
            PublisherId = request.PublisherId,
            Content = request.Content,
            TaskId = request.TaskId,
            ParentCommentId = request.ParentCommentId,
            MentionedUserIds = validIds,
        };

        await _commentsRepository.AddAsync(comment, cancellationToken);
        _logger.LogInformation("Комментарий с ID {CommentId} успешно создан", comment.Id);

        return Result.Ok(comment.Id);
    }
}