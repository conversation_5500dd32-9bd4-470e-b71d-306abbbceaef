using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Utils;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;
using TaskEntity = TeamFlow.Tasks.Core.Entities.Task;

namespace TeamFlow.Tasks.Application.Features.Tasks.Commands;

public sealed class ReorderTaskInColumnCommandHandler : IRequestHandler<ReorderTaskInColumnCommand, Result>
{
    private readonly IRepository<TaskEntity, Guid> _tasksRepository;
    private readonly IRepository<KanbanColumn, Guid> _columnsRepository;
    private readonly ILogger<ReorderTaskInColumnCommandHandler> _logger;
    private readonly IValidator<ReorderTaskInColumnCommand> _validator;

    public ReorderTaskInColumnCommandHandler(
        IRepository<TaskEntity, Guid> tasksRepository,
        IRepository<KanbanColumn, Guid> columnsRepository, 
        ILogger<ReorderTaskInColumnCommandHandler> logger,
        IValidator<ReorderTaskInColumnCommand> validator)
    {
        _tasksRepository = tasksRepository;
        _columnsRepository = columnsRepository;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result> Handle(ReorderTaskInColumnCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Изменение порядка задач в колонке {ColumnId}", request.ColumnId);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "изменении порядка задач");
            return Result.Fail(ErrorsFactory.FromValidationResult("Task", validationResult));
        }

        var column = await _columnsRepository.GetOneAsync(x => x.Id == request.ColumnId, cancellationToken);
        if (column is null)
        {
            _logger.LogWarning("Колонка с идентификатором {ColumnId} не найдена", request.ColumnId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(KanbanColumn),
                $"Колонка с идентификатором {request.ColumnId} не найдена"));
        }

        var tasks = (await _tasksRepository.GetManyAsync(
            t => t.KanbanColumnId == request.ColumnId,
            cancellationToken: cancellationToken)).ToList();

        _logger.LogInformation("Найдено {Count} задач в колонке {ColumnId}", tasks.Count, request.ColumnId);

        var columnTaskIds = tasks.Select(t => t.Id).ToHashSet();
        foreach (var taskId in request.TaskOrder.Keys.Where(taskId => !columnTaskIds.Contains(taskId)))
        {
            _logger.LogWarning("Задача {TaskId} не принадлежит колонке {ColumnId}", taskId, request.ColumnId);
            return Result.Fail(ErrorsFactory.BadRequest("Task",
                $"Задача {taskId} не принадлежит колонке {request.ColumnId}"));
        }

        List<TaskEntity> tasksToUpdate = [];

        foreach (var task in tasks)
        {
            if (!request.TaskOrder.TryGetValue(task.Id, out var newOrder) ||
                !ValueComparer.IsUpdated(task.KanbanOrder, newOrder)) continue;
            task.KanbanOrder = newOrder;
            tasksToUpdate.Add(task);
        }

        if (tasksToUpdate.Count > 0)
        {
            _logger.LogInformation("Обновление порядка для {Count} задач в колонке {ColumnId}", tasksToUpdate.Count,
                request.ColumnId);
            await _tasksRepository.BulkUpdateAsync(tasksToUpdate, cancellationToken);
        }
        else
        {
            _logger.LogInformation("Нет задач для обновления порядка в колонке {ColumnId}", request.ColumnId);
        }

        return Result.Ok();
    }
}