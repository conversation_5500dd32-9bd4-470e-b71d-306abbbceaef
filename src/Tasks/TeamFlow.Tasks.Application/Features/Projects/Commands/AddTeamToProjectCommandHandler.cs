using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Projects.Commands;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Application.Extensions;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Entities;
using Task = System.Threading.Tasks.Task;

namespace TeamFlow.Tasks.Application.Features.Projects.Commands;

public sealed class AddTeamToProjectCommandHandler : IRequestHandler<AddTeamToProjectCommand, Result>
{
    private readonly IRepository<Project, Guid> _projectsRepository;
    private readonly IRepository<Team, Guid> _teamsRepository;
    private readonly ILogger<AddTeamToProjectCommandHandler> _logger;
    private readonly IValidator<AddTeamToProjectCommand> _validator;

    public AddTeamToProjectCommandHandler(
        IRepository<Project, Guid> projectsRepository,
        IRepository<Team, Guid> teamsRepository, 
        ILogger<AddTeamToProjectCommandHandler> logger,
        IValidator<AddTeamToProjectCommand> validator)
    {
        _projectsRepository = projectsRepository;
        _teamsRepository = teamsRepository;
        _logger = logger;
        _validator = validator;
    }

    public async Task<Result> Handle(AddTeamToProjectCommand request, CancellationToken cancellationToken)
    {
        _logger.LogInformation("Добавление команды {TeamId} к проекту {ProjectId}", request.TeamId, request.ProjectId);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogValidationErrors(validationResult.Errors, "добавлении команды к проекту");
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Project), validationResult));
        }

        var projectTask = _projectsRepository.GetOneAsync(x => x.Id == request.ProjectId, cancellationToken);
        var teamTask = _teamsRepository.GetOneAsync(x => x.Id == request.TeamId, cancellationToken);

        await Task.WhenAll(projectTask, teamTask);

        var (project, team) = (projectTask.Result, teamTask.Result);

        if (project is null)
        {
            _logger.LogWarning("Проект с ID {ProjectId} не найден", request.ProjectId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Project), request.ProjectId));
        }

        if (team is null)
        {
            _logger.LogWarning("Команда с ID {TeamId} не найдена", request.TeamId);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Team), request.TeamId));
        }

        // var (project, team) = (await projectTask, await teamTask);

        projectTask.Result!.TeamId = teamTask.Result!.Id;

        await _projectsRepository.UpdateAsync(project, cancellationToken);
        _logger.LogInformation("Команда {TeamId} успешно добавлена к проекту {ProjectId}", request.TeamId,
            request.ProjectId);
        return Result.Ok();
    }
}