using FluentResults;
using FluentValidation;
using MediatR;
using Microsoft.Extensions.Logging;
using TeamFlow.Shared.Contracts.Mapping;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Attachment.Queries;
using TeamFlow.Tasks.Application.Contracts.Contracts.ViewModels;
using TeamFlow.Tasks.Application.Errors;
using TeamFlow.Tasks.Core.Abstractions.Repositories;
using TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;
using TeamFlow.Tasks.Core.Entities;

namespace TeamFlow.Tasks.Application.Features.Attachments.Queries;

public sealed class GetAttachmentByIdQueryHandler : IRequestHandler<GetAttachmentByIdQuery, Result<AttachmentViewModel>>
{
    private readonly IRepository<Attachment, Guid> _attachmentRepository;
    private readonly ILogger<GetAttachmentByIdQueryHandler> _logger;
    private readonly IValidator<GetAttachmentByIdQuery> _validator;
    private readonly IMapper<AttachmentViewModel, Attachment> _mapper;

    public GetAttachmentByIdQueryHandler(IRepository<Attachment, Guid> attachmentRepository, IFileService fileService,
        ILogger<GetAttachmentByIdQueryHandler> logger, IValidator<GetAttachmentByIdQuery> validator,
        IMapper<AttachmentViewModel, Attachment> mapper)
    {
        _attachmentRepository = attachmentRepository;
        _logger = logger;
        _validator = validator;
        _mapper = mapper;
    }
    
    public async Task<Result<AttachmentViewModel>> Handle(GetAttachmentByIdQuery request,
        CancellationToken cancellationToken)
    {
        _logger.LogInformation("Получение вложения по ID: {AttachmentId}", request.Id);

        var validationResult = await _validator.ValidateAsync(request, cancellationToken);
        if (!validationResult.IsValid)
        {
            _logger.LogWarning("Ошибка валидации при получении вложения: {Errors}",
                string.Join(", ", validationResult.Errors.Select(e => e.ErrorMessage)));
            return Result.Fail(ErrorsFactory.FromValidationResult(nameof(Attachment), validationResult));
        }

        var attachment = await _attachmentRepository.GetOneAsync(a => a.Id == request.Id, cancellationToken);

        if (attachment is null)
        {
            _logger.LogWarning("Вложение с ID {AttachmentId} не найдено", request.Id);
            return Result.Fail(ErrorsFactory.NotFound(nameof(Attachment), request.Id));
        }

        _logger.LogInformation("Вложение с ID {AttachmentId} успешно получено", request.Id);
        return Result.Ok(_mapper.MapToModel(attachment));
    }
}