using FluentResults;
using FluentValidation;
using TeamFlow.Tasks.Application.Errors;

namespace TeamFlow.Tasks.Application.Extensions;

/// <summary>
/// Расширения для работы с FluentValidation
/// </summary>
public static class ValidationExtensions
{
    /// <summary>
    /// Выполняет валидацию и возвращает Result
    /// </summary>
    /// <typeparam name="T">Тип валидируемого объекта</typeparam>
    /// <param name="validator">Валидатор</param>
    /// <param name="instance">Валидируемый объект</param>
    /// <param name="entityType">Тип сущности</param>
    /// <param name="cancellationToken">Токен отмены</param>
    /// <returns>Result с результатом валидации</returns>
    public static async Task<Result> ValidateAsync<T>(
        this IValidator<T> validator,
        T instance,
        string entityType,
        CancellationToken cancellationToken = default)
    {
        var validationResult = await validator.ValidateAsync(instance, cancellationToken);

        return validationResult.IsValid
            ? Result.Ok()
            : Result.Fail(ErrorsFactory.FromValidationResult(entityType, validationResult));
    }

    /// <summary>
    /// Выполняет валидацию и возвращает Result с объектом
    /// </summary>
    /// <typeparam name="T">Тип валидируемого объекта</typeparam>
    /// <param name="validator">Валидатор</param>
    /// <param name="instance">Валидируемый объект</param>
    /// <param name="entityType">Тип сущности</param>
    /// <param name="cancellationToken">Токен отмены</param>
    /// <returns>Result с валидируемым объектом или ошибками</returns>
    public static async Task<Result<T>> ValidateAndReturnAsync<T>(
        this IValidator<T> validator,
        T instance,
        string entityType,
        CancellationToken cancellationToken = default)
    {
        var validationResult = await validator.ValidateAsync(instance, cancellationToken);

        return validationResult.IsValid
            ? Result.Ok(instance)
            : Result.Fail<T>(ErrorsFactory.FromValidationResult(entityType, validationResult));
    }
}