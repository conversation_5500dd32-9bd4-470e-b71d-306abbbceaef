using TeamFlow.Tasks.Application.Settings;
using TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;
using TeamFlow.Tasks.Core.Contracts;
using TeamFlow.Tasks.Core.Utils;

namespace TeamFlow.Tasks.Application.Services;

public sealed class FileService : IFileService
{
    private readonly IFileServiceSettings _settings;

    private readonly IFileTypeProvider _contentTypeProvider;

    public FileService(IFileServiceSettings settings, IFileTypeProvider contentTypeProvider)
    {
        _settings = settings;
        _contentTypeProvider = contentTypeProvider;

        if (!Directory.Exists(_settings.BaseStoragePath))
        {
            Directory.CreateDirectory(_settings.BaseStoragePath);
        }
    }

    public async Task<FileInfoResponse> SaveFileAsync(Stream fileStream, string originalFileName,
        string? directoryPath = null, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(originalFileName))
        {
            throw new ArgumentNullException(nameof(originalFileName));
        }

        string targetDirectory = directoryPath ?? _settings.BaseStoragePath;

        if (!Directory.Exists(targetDirectory))
        {
            Directory.CreateDirectory(targetDirectory);
        }

        string fileExtension = Path.GetExtension(originalFileName);
        string uniqueFileName = GenerateUniqueFileName(fileExtension);
        string fullPath = Path.Combine(targetDirectory, uniqueFileName);

        await using var writingStream = new FileStream(fullPath, FileMode.Create);
        await fileStream.CopyToAsync(writingStream, cancellationToken);

        if (!_contentTypeProvider.TryGetContentType(uniqueFileName, out var contentType))
        {
            contentType = "application/octet-stream";
        }

        FileInfo fileInfo = new(fullPath);

        return new FileInfoResponse(uniqueFileName, originalFileName, fullPath, (ulong)fileInfo.Length, contentType);
    }

    public async Task<bool> DeleteFileAsync(string fileName, CancellationToken cancellationToken = default)
    {
        ArgumentException.ThrowIfNullOrWhiteSpace(fileName, nameof(fileName));
        
        var path = GetFilePath(fileName);

        try
        {
            if (!File.Exists(path))
            {
                return true;
            }

            await System.Threading.Tasks.Task.Run(() => File.Delete(path), cancellationToken);
            return true;
        }
        catch (IOException)
        {
            return false;
        }
        catch (UnauthorizedAccessException)
        {
            return false;
        }
        catch (Exception)
        {
            return false;
        }
    }

    public string GetFilePath(string fileName)
    {
        return Path.Combine(_settings.BaseStoragePath, fileName);
    }

    private string GenerateUniqueFileName(string fileExtension)
    {
        string timestamp = DateTimeOffset.UtcNow.ToUnixTimeMilliseconds().ToString();
        string uniqueId = Guid.NewGuid().ToString("N");

        return $"{uniqueId}_{timestamp}{fileExtension}";
    }
}