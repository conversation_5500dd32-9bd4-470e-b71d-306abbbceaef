using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;

namespace TeamFlow.Tasks.Application.Validators.Task.NewA;

public class MoveTaskToColumnCommandValidator : AbstractValidator<MoveTaskToColumnCommand>
{
    public MoveTaskToColumnCommandValidator()
    {
        RuleFor(x => x.TaskId)
            .NotEmpty().WithMessage("Идентификатор задачи не может быть пустым");
                
        RuleFor(x => x.ColumnId)
            .NotEmpty().WithMessage("Идентификатор колонки не может быть пустым");
    }
}