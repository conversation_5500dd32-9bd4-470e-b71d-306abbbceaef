using FluentValidation;
using TeamFlow.Tasks.Application.Contracts.Contracts.Features.Task.Commands;

namespace TeamFlow.Tasks.Application.Validators.Task.NewA;

public class RemoveTagCommandValidator : AbstractValidator<RemoveTagCommand>
{
    public RemoveTagCommandValidator()
    {
        RuleFor(x => x.TaskId)
            .NotEmpty().WithMessage("Идентификатор задачи не может быть пустым");
                
        RuleFor(x => x.Tag)
            .NotEmpty().WithMessage("Тег не может быть пустым")
            .MaximumLength(50).WithMessage("Тег не может превышать 50 символов");
    }
}