using System.Net;
using TeamFlow.Tasks.Core.Shared.Exceptions.Base;

namespace TeamFlow.Tasks.Core.Shared.Exceptions;

public class ConflictException : HttpException
{
    public ConflictException() : base(HttpStatusCode.Conflict)
    {
    }

    public ConflictException(string message) : base(HttpStatusCode.Conflict, message)
    {
    }

    public ConflictException(string name, object key) : base(HttpStatusCode.Conflict,
        $"\"{name}\" with {key} already exists.")
    {
    }

    public ConflictException(string message, Exception innerException)
        : base(HttpStatusCode.Conflict, message, innerException)
    {
    }
}