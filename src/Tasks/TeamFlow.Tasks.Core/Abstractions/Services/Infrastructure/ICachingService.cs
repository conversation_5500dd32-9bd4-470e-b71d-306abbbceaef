namespace TeamFlow.Tasks.Core.Abstractions.Services.Infrastructure;

public interface ICachingService
{
    Task<T?> GetAsync<T>(string key, CancellationToken cancellationToken = default) where T : class;

    Task<bool> SetAsync<T>(string key, T value, TimeSpan? expiration = null,
        CancellationToken cancellationToken = default) where T : class;

    // ValueTask<bool> SetAsync<T>(string key, T value, TimeSpan? expiration = null,
        // CancellationToken cancellationToken = default) where T : class;

    Task<bool> InvalidateAsync(string key, CancellationToken cancellationToken = default);

    ValueTask<bool> ExistsAsync(string key, CancellationToken cancellationToken = default);
}