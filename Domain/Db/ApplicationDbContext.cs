using Microsoft.EntityFrameworkCore;
using VirtuManagerBackend.Domain.Db.Tables.BridgeIpPoolTable;
using VirtuManagerBackend.Domain.Db.Tables.BridgesTable;
using VirtuManagerBackend.Domain.Db.Tables.BridgeTaskTable;
using VirtuManagerBackend.Domain.Db.Tables.ImageRepositoryTable;
using VirtuManagerBackend.Domain.Db.Tables.NodeTable;
using VirtuManagerBackend.Domain.Db.Tables.UserTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualDiskTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualDiskTaskTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineConfigurationTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineSettingsTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTable;
using VirtuManagerBackend.Domain.Db.Tables.VirtualMachineTask;

namespace VirtuManagerBackend.Domain.Db;

public class ApplicationDbContext : DbContext
{
	public DbSet<User> Users { get; private set; }

	public DbSet<Node> Nodes { get; private set; }
	
	public DbSet<VirtualMachine> VirtualMachines { get; private set; }
	
	public DbSet<VirtualMachineSettings> VirtualMachineSettings { get; private set; }
	
	public DbSet<VirtualDisk> VirtualDisks { get; private set; }

	public DbSet<VirtualMachineTask> VirtualMachineTasks { get; private set; }
	
	public DbSet<Bridge> Bridges { get; private set; }

	public DbSet<BridgeTask> BridgeTasks { get; private set; }

	public DbSet<BridgeIp> IpPool { get; private set; }
	
	public DbSet<VirtualDiskTask> VirtualDiskTasks { get; private set; }

	public DbSet<RemoteImageRepository> ImageRepositories { get; private set; }

	public DbSet<VirtualMachineConfiguration> VirtualMachineConfigurations { get; private set; }

	private readonly string? _connectionString;

	public ApplicationDbContext(IConfiguration configuration)
	{
		ArgumentNullException.ThrowIfNull(configuration);

		_connectionString = configuration.GetConnectionString("DefaultConnection");

		ArgumentNullException.ThrowIfNull(_connectionString);
	}

	protected override void OnModelCreating(ModelBuilder modelBuilder)
	{
		modelBuilder.Entity<User>().HasData(new User
		{
			Id = 1,
			Login = "admin",
			Role = UserRole.Admin,
			Password = "admin",
			Email = "<EMAIL>",
			CreatedAt = DateTime.Now
		});

		modelBuilder.Entity<User>().HasData(new User
		{
			Id = 2,
			Login = "developer",
			Role = UserRole.Developer,
			Password = "12345",
			Email = "<EMAIL>",
			CreatedAt = DateTime.Now
		});

		modelBuilder.Entity<Node>().HasData(new Node
		{
			Id = 1,
			PrivateKey = "41fa7636-195b-49d1-b346-3143648bfcb3",
			Title = "Node"
		});

		modelBuilder.Entity<User>().HasKey(k => k.Id);
		modelBuilder.Entity<Node>().HasKey(k => k.Id);
		modelBuilder.Entity<VirtualMachine>().HasKey(k => k.Id);
		modelBuilder.Entity<VirtualMachineSettings>().HasKey(k => k.Id);
		modelBuilder.Entity<VirtualDisk>().HasKey(k => k.Id);

		modelBuilder.Entity<Node>()
			.HasMany(n => n.VirtualMachines)
			.WithOne(vm => vm.Node)
			.HasForeignKey(vm => vm.NodeId);

		modelBuilder.Entity<VirtualMachine>()
			.HasOne(vm => vm.VirtualMachineSettings)
			.WithOne(vms => vms.VirtualMachine)
			.HasForeignKey<VirtualMachineSettings>(vms => vms.VirtualMachineId);

		modelBuilder.Entity<VirtualMachineSettings>()
			.HasMany(vms => vms.VirtualDisks)
			.WithOne(vd => vd.VirtualMachineSettings)
			.HasForeignKey(vd => vd.VirtualMachineSettingsId);

		modelBuilder.Entity<VirtualMachineTask>()
			.Property(e => e.Status)
			.HasConversion<string>();

		modelBuilder.Entity<VirtualMachineTask>()
			.Property(e => e.Type)
			.HasConversion<string>();

		modelBuilder.Entity<User>()
			.Property(u => u.Role)
			.HasConversion<string>();
		
		modelBuilder.Entity<VirtualDisk>()
			.HasOne(vd => vd.Node)
			.WithMany(n => n.VirtualDisks)
			.HasForeignKey(vd => vd.NodeId);

		modelBuilder.Entity<Bridge>()
			.HasOne(b => b.Node)
			.WithMany(n => n.Bridges)
			.HasForeignKey(b => b.NodeId);

		modelBuilder.Entity<BridgeIp>()
			.HasOne(b => b.Bridge)
			.WithMany(b => b.BridgeIps)
			.HasForeignKey(v => v.BridgeId);
	}

	protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
	{
		optionsBuilder.UseMySql(_connectionString, ServerVersion.AutoDetect(_connectionString));
	}
}