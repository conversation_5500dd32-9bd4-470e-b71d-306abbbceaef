using System.Linq.Expressions;

namespace TeamFlow.Shared.Repositories.Repositories.Common;

public interface IBulkOperations<TEntity, in TKey> where TEntity : class
    where TKey : IEquatable<TKey>
{
    /// <summary>
    /// Добавляет сразу несколько сущностей в хранилище (асинхронно).
    /// </summary>
    /// <param name="entities">Коллекция добавляемых сущностей.</param>
    /// <param name="cancellationToken">Токен отмены.</param>
    System.Threading.Tasks.Task BulkInsertAsync(IEnumerable<TEntity> entities,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Обновляет сразу несколько сущностей в хранилище (асинхронно).
    /// </summary>
    /// <param name="entities">Коллекция обновляемых сущностей.</param>
    /// <param name="cancellationToken">Токен отмены.</param>
    System.Threading.Tasks.Task BulkUpdateAsync(IEnumerable<TEntity> entities,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Удаляет сразу несколько сущностей из хранилища (асинхронно).
    /// </summary>
    /// <param name="ids">Коллекция идентификаторов удаляемых сущностей.</param>
    /// <param name="cancellationToken">Токен отмены.</param>
    System.Threading.Tasks.Task BulkDeleteAsync(IEnumerable<TKey> ids,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// Удаляет сразу несколько сущностей из хранилища (асинхронно) по заданному условию.
    /// </summary>
    /// <param name="predicate">Условие по которому удаляет сущности.</param>
    /// <param name="cancellationToken">Токен отмены операции.</param>
    /// <returns></returns>
    System.Threading.Tasks.Task BulkDeleteAsync(Expression<Func<TEntity, bool>> predicate,
        CancellationToken cancellationToken = default);


    // System.Threading.Tasks.Task BulkUpsertAsync(IEnumerable<TEntity> entities,
    // CancellationToken cancellationToken = default);
}