using Grpc.Core;
using Microsoft.AspNetCore.Authorization;
using virtu_manager_auth_service;
using virtu_manager_auth.Mapping;
using VirtuManager.Auth.Api.Services.Interfaces;

namespace virtu_manager_auth.Services.Grpc;

[Authorize]
public class GetUserByLoginService : GrpcUserService.GrpcUserServiceBase
{
    private readonly IGetUsersService _getUsersService;

    public GetUserByLoginService(IGetUsersService getUsersService)
    {
        _getUsersService = getUsersService ?? throw new ArgumentNullException(nameof(getUsersService));
    }

    public override async Task<User> GetUserByLogin(GetUserByLoginRequest request, ServerCallContext context)
    {
        if (string.IsNullOrWhiteSpace(request.Login))
            throw new RpcException(new Status(StatusCode.InvalidArgument, "Login field is required"));
        
        var user = await _getUsersService.GetByLogin(request.Login);
        
        return user.ToGrpcModel();
    }
}