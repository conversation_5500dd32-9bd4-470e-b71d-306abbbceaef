using Microsoft.EntityFrameworkCore;
using VirtuManager.Auth.Api.Repositories;
using VirtuManager.Auth.Domain.Entities;
using VirtuManager.Auth.Infrastructure.Database;

namespace virtu_manager_auth.Repositories;

public class UsersRepository : AbstractRepository<User>
{
    public UsersRepository(ApplicationDbContext applicationDbContext) : base(applicationDbContext) {}

    public override async Task Add(User entity)
    {
        await ApplicationDbContext.Users.AddAsync(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public override async Task<List<User>> GetAll() => await ApplicationDbContext.Users.ToListAsync();

    public override async Task Remove(User entity)
    {
        ApplicationDbContext.Remove(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    public override async Task Update(User entity)
    {
        ApplicationDbContext.Update(entity);
        await ApplicationDbContext.SaveChangesAsync();
    }

    // public async Task<User?> GetUserByLoginAndPassword(string login, string password)
    // {
    //     ArgumentNullException.ThrowIfNull(password);
    //     ArgumentNullException.ThrowIfNull(login);
    //
    //     return await ApplicationDbContext.Users.FirstOrDefaultAsync(u => u.Login == login && u.PasswordHash == password);
    // }

    public async Task<User?> GetUserById(Guid id)
        => await ApplicationDbContext.Users.FirstOrDefaultAsync(u => u.Id == id);

    public async Task<User?> GetUserByLogin(string login)
        => await ApplicationDbContext.Users.FirstOrDefaultAsync(u => u.Login == login);
    
    public async Task<(IEnumerable<User>? users, long totalCount)> GetUsersPaged(int pageNumber, int pageSize) 
        => await GetPaged(pageNumber, pageSize, ApplicationDbContext.Users.AsQueryable());

}